#!/usr/bin/env python3
"""
Fix Data Structure Issues
Comprehensive fix for 64 failed data files with missing major names and data structure inconsistencies.
"""

import sys
import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, List

def analyze_data_structure_issues():
    """Analyze all data files to identify structure issues"""
    print("🔍 ANALYZING DATA STRUCTURE ISSUES")
    print("=" * 60)
    
    data_dir = Path("data/raw")
    issues = {
        "missing_major_names": [],
        "wrong_structure": [],
        "string_instead_of_dict": [],
        "missing_university_info": [],
        "total_files": 0,
        "valid_files": 0
    }
    
    for json_file in data_dir.rglob("*.json"):
        issues["total_files"] += 1
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check if data is a string instead of dict
            if isinstance(data, str):
                issues["string_instead_of_dict"].append(str(json_file))
                continue
            
            if not isinstance(data, dict):
                issues["wrong_structure"].append(str(json_file))
                continue
            
            # Check university info
            has_university = False
            if 'university' in data or 'university_info' in data:
                has_university = True
            elif 'name_kh' in data and 'name_en' in data:
                has_university = True
            
            if not has_university:
                issues["missing_university_info"].append(str(json_file))
            
            # Check majors structure
            if 'majors' not in data:
                issues["wrong_structure"].append(str(json_file))
                continue
            
            majors = data['majors']
            if not isinstance(majors, list):
                issues["wrong_structure"].append(str(json_file))
                continue
            
            file_has_issues = False
            for i, major in enumerate(majors):
                if not isinstance(major, dict):
                    issues["string_instead_of_dict"].append(f"{json_file} (major {i})")
                    file_has_issues = True
                    continue
                
                # Check for major names in different locations
                has_name_kh = False
                has_name_en = False
                
                # Direct in major
                if 'name_kh' in major and 'name_en' in major:
                    has_name_kh = True
                    has_name_en = True
                
                # In major_info
                elif 'major_info' in major and isinstance(major['major_info'], dict):
                    major_info = major['major_info']
                    if 'name_kh' in major_info and 'name_en' in major_info:
                        has_name_kh = True
                        has_name_en = True
                
                if not (has_name_kh and has_name_en):
                    issues["missing_major_names"].append(f"{json_file} (major {i})")
                    file_has_issues = True
            
            if not file_has_issues:
                issues["valid_files"] += 1
                
        except Exception as e:
            issues["wrong_structure"].append(f"{json_file} (error: {e})")
    
    # Print analysis results
    print(f"📊 ANALYSIS RESULTS:")
    print(f"  Total files: {issues['total_files']}")
    print(f"  Valid files: {issues['valid_files']}")
    print(f"  Files with issues: {issues['total_files'] - issues['valid_files']}")
    print()
    
    print(f"🔴 Missing major names: {len(issues['missing_major_names'])}")
    if issues['missing_major_names'][:5]:  # Show first 5
        for issue in issues['missing_major_names'][:5]:
            print(f"    {issue}")
        if len(issues['missing_major_names']) > 5:
            print(f"    ... and {len(issues['missing_major_names']) - 5} more")
    print()
    
    print(f"🔴 Wrong structure: {len(issues['wrong_structure'])}")
    if issues['wrong_structure'][:5]:
        for issue in issues['wrong_structure'][:5]:
            print(f"    {issue}")
        if len(issues['wrong_structure']) > 5:
            print(f"    ... and {len(issues['wrong_structure']) - 5} more")
    print()
    
    print(f"🔴 String instead of dict: {len(issues['string_instead_of_dict'])}")
    if issues['string_instead_of_dict'][:5]:
        for issue in issues['string_instead_of_dict'][:5]:
            print(f"    {issue}")
        if len(issues['string_instead_of_dict']) > 5:
            print(f"    ... and {len(issues['string_instead_of_dict']) - 5} more")
    print()
    
    print(f"🔴 Missing university info: {len(issues['missing_university_info'])}")
    if issues['missing_university_info'][:5]:
        for issue in issues['missing_university_info'][:5]:
            print(f"    {issue}")
        if len(issues['missing_university_info']) > 5:
            print(f"    ... and {len(issues['missing_university_info']) - 5} more")
    
    return issues

def fix_data_structure_issues():
    """Fix all identified data structure issues"""
    print("\n🔧 FIXING DATA STRUCTURE ISSUES")
    print("=" * 60)
    
    data_dir = Path("data/raw")
    backup_dir = Path("data/backup_before_fix")
    
    # Create backup
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    shutil.copytree(data_dir, backup_dir)
    print(f"✅ Created backup at {backup_dir}")
    
    fixed_files = 0
    failed_files = []
    
    for json_file in data_dir.rglob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Skip if data is not a dict
            if not isinstance(data, dict):
                failed_files.append(f"{json_file}: Not a dictionary")
                continue
            
            # Skip if no majors
            if 'majors' not in data or not isinstance(data['majors'], list):
                failed_files.append(f"{json_file}: No valid majors array")
                continue
            
            file_modified = False
            
            # Fix majors structure
            for i, major in enumerate(data['majors']):
                if not isinstance(major, dict):
                    failed_files.append(f"{json_file}: Major {i} is not a dictionary")
                    continue
                
                # Fix missing name_kh and name_en
                if 'name_kh' not in major or 'name_en' not in major:
                    # Try to get from major_info
                    if 'major_info' in major and isinstance(major['major_info'], dict):
                        major_info = major['major_info']
                        if 'name_kh' in major_info:
                            major['name_kh'] = major_info['name_kh']
                            file_modified = True
                        if 'name_en' in major_info:
                            major['name_en'] = major_info['name_en']
                            file_modified = True
                    
                    # If still missing, create placeholder names
                    if 'name_kh' not in major:
                        major['name_kh'] = f"កម្មវិធីសិក្សា {i+1}"
                        file_modified = True
                    if 'name_en' not in major:
                        major['name_en'] = f"Program {i+1}"
                        file_modified = True
            
            # Ensure university structure is consistent
            if 'university' not in data:
                if 'university_info' in data:
                    data['university'] = data['university_info']
                    file_modified = True
                elif 'name_kh' in data and 'name_en' in data:
                    # Create university object from root level data
                    data['university'] = {
                        'name_kh': data.get('name_kh', 'សាកលវិទ្យាល័យ'),
                        'name_en': data.get('name_en', 'University'),
                        'id': json_file.stem,
                        'city': data.get('location', {}).get('city', 'Phnom Penh') if isinstance(data.get('location'), dict) else 'Phnom Penh',
                        'type': data.get('type', 'ឯកជន'),
                        'website': data.get('contact', {}).get('website', '') if isinstance(data.get('contact'), dict) else '',
                        'phone': data.get('contact', {}).get('phone', [''])[0] if isinstance(data.get('contact'), dict) and isinstance(data.get('contact', {}).get('phone'), list) else '',
                        'email': data.get('contact', {}).get('email', '') if isinstance(data.get('contact'), dict) else ''
                    }
                    file_modified = True
            
            # Save if modified
            if file_modified:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                fixed_files += 1
                print(f"✅ Fixed: {json_file.name}")
                
        except Exception as e:
            failed_files.append(f"{json_file}: {e}")
    
    print(f"\n📊 FIX RESULTS:")
    print(f"  Fixed files: {fixed_files}")
    print(f"  Failed files: {len(failed_files)}")
    
    if failed_files:
        print(f"\n🔴 FAILED FILES:")
        for failure in failed_files[:10]:  # Show first 10
            print(f"    {failure}")
        if len(failed_files) > 10:
            print(f"    ... and {len(failed_files) - 10} more")
    
    return fixed_files, failed_files

def create_enhanced_data_loader():
    """Create an enhanced data loader that handles multiple data formats"""
    print("\n🔧 CREATING ENHANCED DATA LOADER")
    print("=" * 60)
    
    enhanced_loader_code = '''"""
Enhanced Data Loader Module
Handles multiple data structure formats and provides robust error handling
"""

import json
import os
import logging
from functools import lru_cache
from pathlib import Path
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

@lru_cache(maxsize=1)
def load_raw(data_dir: str = None) -> List[Dict[str, Any]]:
    """
    Load all raw university JSON files with enhanced error handling
    
    Args:
        data_dir: Optional custom data directory path (for testing)
    
    Returns:
        List[Dict]: Combined list of all university programs
    """
    # Get project root directory (2 levels up from this file)
    root = Path(__file__).parents[2]
    all_programs = []
    
    # Use custom data directory if provided (for testing)
    if data_dir:
        search_path = Path(data_dir)
        json_files = list(search_path.glob("**/*.json"))
    else:
        # Recursively find all .json files in default location
        json_files = list(root.glob("data/raw/**/*.json"))
    
    logger.info(f"Found {len(json_files)} JSON files to process")
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            # Handle string data (should not happen but be safe)
            if isinstance(file_data, str):
                logger.error(f"File {json_file} contains string instead of JSON object")
                continue
            
            if not isinstance(file_data, dict):
                logger.error(f"File {json_file} does not contain a valid JSON object")
                continue
            
            # Extract university info with multiple format support
            university = extract_university_info(file_data, json_file)
            
            # Extract majors with multiple format support
            majors = extract_majors_info(file_data, json_file)
            
            if not majors:
                logger.warning(f"No valid majors found in {json_file}")
                continue
            
            # Extract location and contact info
            location = extract_location_info(file_data)
            contact = extract_contact_info(file_data)
            
            # Combine university info with each major
            for major in majors:
                program = create_program_dict(university, major, location, contact, json_file)
                
                if validate_program_data(program):
                    all_programs.append(program)
                else:
                    logger.warning(f"Invalid program data in {json_file}: {major.get('name_kh', 'Unknown')}")
        
        except Exception as e:
            logger.error(f"Error loading {json_file}: {e}")
            continue
    
    logger.info(f"Successfully loaded {len(all_programs)} programs from {len(json_files)} files")
    return all_programs

def extract_university_info(file_data: Dict[str, Any], json_file: Path) -> Dict[str, Any]:
    """Extract university information from various data formats"""
    university = {}
    
    # Try different university info locations
    if 'university' in file_data and isinstance(file_data['university'], dict):
        university = file_data['university'].copy()
    elif 'university_info' in file_data and isinstance(file_data['university_info'], dict):
        university = file_data['university_info'].copy()
    else:
        # Create from root level data
        university = {
            'id': json_file.stem,
            'name_kh': file_data.get('name_kh', 'សាកលវិទ្យាល័យ'),
            'name_en': file_data.get('name_en', 'University'),
            'type': file_data.get('type', 'ឯកជន'),
            'city': 'Phnom Penh'  # Default
        }
    
    # Ensure required fields
    if 'id' not in university:
        university['id'] = json_file.stem
    if 'name_kh' not in university:
        university['name_kh'] = 'សាកលវិទ្យាល័យ'
    if 'name_en' not in university:
        university['name_en'] = 'University'
    if 'city' not in university:
        university['city'] = 'Phnom Penh'
    
    return university

def extract_majors_info(file_data: Dict[str, Any], json_file: Path) -> List[Dict[str, Any]]:
    """Extract majors information with enhanced error handling"""
    if 'majors' not in file_data:
        return []
    
    majors_data = file_data['majors']
    if not isinstance(majors_data, list):
        logger.error(f"Majors in {json_file} is not a list")
        return []
    
    valid_majors = []
    for i, major in enumerate(majors_data):
        if not isinstance(major, dict):
            logger.error(f"Major {i} in {json_file} is not a dictionary")
            continue
        
        # Ensure name_kh and name_en exist
        if 'name_kh' not in major or 'name_en' not in major:
            # Try to get from major_info
            if 'major_info' in major and isinstance(major['major_info'], dict):
                major_info = major['major_info']
                if 'name_kh' in major_info:
                    major['name_kh'] = major_info['name_kh']
                if 'name_en' in major_info:
                    major['name_en'] = major_info['name_en']
        
        # If still missing, skip this major
        if 'name_kh' not in major or 'name_en' not in major:
            logger.error(f"Major {i} in {json_file} missing name_kh or name_en")
            continue
        
        valid_majors.append(major)
    
    return valid_majors

def extract_location_info(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract location information"""
    location = {}
    if 'location' in file_data and isinstance(file_data['location'], dict):
        location = file_data['location']
    return location

def extract_contact_info(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract contact information"""
    contact = {}
    if 'contact' in file_data and isinstance(file_data['contact'], dict):
        contact = file_data['contact']
    return contact

def create_program_dict(university: Dict[str, Any], major: Dict[str, Any], 
                       location: Dict[str, Any], contact: Dict[str, Any], 
                       json_file: Path) -> Dict[str, Any]:
    """Create a standardized program dictionary"""
    
    def safe_get(obj, key, default=''):
        """Safely get value from dict or return default"""
        if isinstance(obj, dict):
            return obj.get(key, default)
        return default
    
    return {
        # University information
        'university_id': safe_get(university, 'id', json_file.stem),
        'university_name_kh': safe_get(university, 'name_kh', 'សាកលវិទ្យាល័យ'),
        'university_name_en': safe_get(university, 'name_en', 'University'),
        'university_type': safe_get(university, 'type', 'ឯកជន'),
        'city': safe_get(university, 'city', safe_get(location, 'city', 'Phnom Penh')),
        'website': safe_get(university, 'website', safe_get(contact, 'website', '')),
        'phone': safe_get(university, 'phone', safe_get(contact, 'phone', [''])[0] if isinstance(safe_get(contact, 'phone'), list) else ''),
        'email': safe_get(university, 'email', safe_get(contact, 'email', '')),
        'address': safe_get(location, 'address_kh', safe_get(university, 'location', '')),
        
        # Major information
        'major_id': safe_get(major, 'id', f"{json_file.stem}_{safe_get(major, 'name_en', 'program').lower().replace(' ', '_')}"),
        'major_name_kh': safe_get(major, 'name_kh', 'កម្មវិធីសិក្សា'),
        'major_name_en': safe_get(major, 'name_en', 'Program'),
        'degree_level': safe_get(major, 'degree_level_kh', safe_get(major.get('major_info', {}), 'degree_level_kh', 'បរិញ្ញាបត្រ')),
        'study_duration': safe_get(major, 'study_duration_kh', safe_get(major.get('major_info', {}), 'study_duration_kh', '៤ ឆ្នាំ')),
        'language_of_instruction': safe_get(major, 'language_of_instruction', safe_get(major.get('major_info', {}), 'language_of_instruction', ['ខ្មែរ'])),
        
        # Financial information
        'tuition_fees_usd': safe_get(major.get('practical_information', {}), 'tuition_fees_usd', ''),
        'tuition_fees_khr': safe_get(major.get('practical_information', {}), 'tuition_fees_khr', ''),
        'scholarship_availability': safe_get(major.get('practical_information', {}), 'scholarship_availability', False),
        
        # Career information
        'potential_careers_kh': safe_get(major.get('career_prospects', {}), 'potential_careers_kh', []),
        'potential_careers_en': safe_get(major.get('career_prospects', {}), 'potential_careers_en', []),
        'employment_rate': safe_get(major.get('career_prospects', {}).get('employment_statistics', {}), 'employment_rate', ''),
        'average_starting_salary': safe_get(major.get('career_prospects', {}).get('employment_statistics', {}), 'average_starting_salary', ''),
        
        # Academic information
        'total_credits': safe_get(major.get('program_details', {}), 'total_credits', 0) or 0,
        'minimum_gpa': safe_get(major.get('academic_requirements', {}), 'minimum_gpa', 0.0) or 0.0,
        
        # Source tracking
        'source_file': str(json_file),
    }

def validate_program_data(program: Dict[str, Any]) -> bool:
    """Validate that a program dict has required fields"""
    if not isinstance(program, dict):
        return False
    
    required_fields = [
        'university_name_kh', 'university_name_en',
        'major_name_kh', 'major_name_en'
    ]
    
    for field in required_fields:
        if field not in program or not program[field]:
            return False
    
    return True
'''
    
    # Write the enhanced data loader
    enhanced_loader_path = Path("src/core/enhanced_data_loader.py")
    with open(enhanced_loader_path, 'w', encoding='utf-8') as f:
        f.write(enhanced_loader_code)
    
    print(f"✅ Created enhanced data loader at {enhanced_loader_path}")
    return enhanced_loader_path

def main():
    """Main function to analyze and fix data structure issues"""
    print("🚀 DATA STRUCTURE ISSUE FIXER")
    print("=" * 60)
    
    # Step 1: Analyze issues
    issues = analyze_data_structure_issues()
    
    # Step 2: Fix issues
    fixed_files, failed_files = fix_data_structure_issues()
    
    # Step 3: Create enhanced data loader
    enhanced_loader_path = create_enhanced_data_loader()
    
    print(f"\n🎉 DATA STRUCTURE FIX COMPLETE!")
    print(f"  📊 Total files analyzed: {issues['total_files']}")
    print(f"  ✅ Files fixed: {fixed_files}")
    print(f"  ❌ Files failed: {len(failed_files)}")
    print(f"  🔧 Enhanced loader created: {enhanced_loader_path}")
    
    if len(failed_files) == 0:
        print(f"\n🎯 ALL DATA STRUCTURE ISSUES RESOLVED!")
        return True
    else:
        print(f"\n⚠️ {len(failed_files)} files still have issues")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Fix interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Fix crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
