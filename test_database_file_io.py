#!/usr/bin/env python3
"""
Test Database and File I/O Issues
Tests for file permissions, disk space handling, concurrent access, and data corruption protection.
"""

import sys
import os
import tempfile
import json
import time
import threading
import shutil
import hashlib
from pathlib import Path
from unittest.mock import patch, MagicMock
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_file_permission_checks():
    """Test file permission validation and error handling"""
    print("🧪 Testing file permission checks...")
    
    try:
        # Test file access validation
        try:
            from src.core.data.loader import validate_file_access
            
            # Test with valid file
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                temp_file.write('{"test": "data"}')
                temp_file_path = Path(temp_file.name)
            
            if validate_file_access(temp_file_path):
                print("  ✅ Valid file access validation working")
                valid_file_check = True
            else:
                print("  ❌ Valid file access validation failed")
                valid_file_check = False
            
            os.unlink(temp_file_path)
            
        except Exception as e:
            print(f"  ❌ File access validation test failed: {e}")
            valid_file_check = False
        
        # Test with non-existent file
        try:
            from src.core.data.loader import validate_file_access
            
            non_existent = Path("/non/existent/file.json")
            if not validate_file_access(non_existent):
                print("  ✅ Non-existent file properly rejected")
                nonexistent_check = True
            else:
                print("  ❌ Non-existent file not properly rejected")
                nonexistent_check = False
                
        except Exception as e:
            print(f"  ❌ Non-existent file test failed: {e}")
            nonexistent_check = False
        
        # Test with oversized file
        try:
            from src.core.data.loader import validate_file_access, MAX_FILE_SIZE
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                # Write data larger than MAX_FILE_SIZE
                large_data = "x" * (MAX_FILE_SIZE + 1000)
                temp_file.write(large_data)
                large_file_path = Path(temp_file.name)
            
            if not validate_file_access(large_file_path):
                print("  ✅ Oversized file properly rejected")
                size_check = True
            else:
                print("  ❌ Oversized file not properly rejected")
                size_check = False
            
            os.unlink(large_file_path)
            
        except Exception as e:
            print(f"  ❌ Oversized file test failed: {e}")
            size_check = False
        
        success_rate = sum([valid_file_check, nonexistent_check, size_check]) / 3
        
        if success_rate >= 0.8:
            print(f"  ✅ File permission checks working ({success_rate:.1%})")
            return True
        else:
            print(f"  ❌ File permission checks insufficient ({success_rate:.1%})")
            return False
            
    except Exception as e:
        print(f"  ❌ File permission checks test failed: {e}")
        return False

def test_disk_space_handling():
    """Test disk space monitoring and handling"""
    print("🧪 Testing disk space handling...")
    
    try:
        # Test disk space checking
        try:
            import shutil
            
            # Get current disk usage
            total, used, free = shutil.disk_usage(Path.cwd())
            
            # Convert to MB for readability
            free_mb = free / (1024 * 1024)
            
            if free_mb > 100:  # At least 100MB free
                print(f"  ✅ Sufficient disk space available ({free_mb:.1f} MB)")
                space_check = True
            else:
                print(f"  ⚠️ Low disk space ({free_mb:.1f} MB)")
                space_check = True  # Not necessarily a failure
                
        except Exception as e:
            print(f"  ❌ Disk space check failed: {e}")
            space_check = False
        
        # Test large file creation handling
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Try to create a reasonably large file (1MB)
                large_file = temp_path / "large_test.json"
                test_data = {"data": "x" * 1000000}  # 1MB of data
                
                with open(large_file, 'w') as f:
                    json.dump(test_data, f)
                
                if large_file.exists() and large_file.stat().st_size > 500000:
                    print("  ✅ Large file creation handling working")
                    large_file_handling = True
                else:
                    print("  ❌ Large file creation failed")
                    large_file_handling = False
                    
        except Exception as e:
            print(f"  ❌ Large file creation test failed: {e}")
            large_file_handling = False
        
        # Test disk space monitoring during operations
        try:
            # Simulate checking available space before operations
            def check_available_space(path: Path, required_mb: int = 10) -> bool:
                try:
                    total, used, free = shutil.disk_usage(path)
                    free_mb = free / (1024 * 1024)
                    return free_mb >= required_mb
                except Exception:
                    return False
            
            if check_available_space(Path.cwd(), 10):
                print("  ✅ Disk space monitoring working")
                space_monitoring = True
            else:
                print("  ❌ Disk space monitoring failed")
                space_monitoring = False
                
        except Exception as e:
            print(f"  ❌ Disk space monitoring test failed: {e}")
            space_monitoring = False
        
        success_rate = sum([space_check, large_file_handling, space_monitoring]) / 3
        
        if success_rate >= 0.8:
            print(f"  ✅ Disk space handling working ({success_rate:.1%})")
            return True
        else:
            print(f"  ❌ Disk space handling insufficient ({success_rate:.1%})")
            return False
            
    except Exception as e:
        print(f"  ❌ Disk space handling test failed: {e}")
        return False

def test_concurrent_file_access():
    """Test concurrent file access protection"""
    print("🧪 Testing concurrent file access...")
    
    try:
        # Test concurrent read operations
        try:
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                test_data = {"test": "concurrent_read", "data": list(range(1000))}
                json.dump(test_data, temp_file)
                temp_file_path = temp_file.name
            
            def read_file():
                try:
                    with open(temp_file_path, 'r') as f:
                        data = json.load(f)
                    return len(data.get('data', []))
                except Exception as e:
                    return -1
            
            # Run multiple concurrent reads
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(read_file) for _ in range(10)]
                results = [future.result() for future in as_completed(futures)]
            
            # All reads should succeed and return same result
            if all(result == 1000 for result in results):
                print("  ✅ Concurrent read operations working")
                concurrent_reads = True
            else:
                print(f"  ❌ Concurrent read operations failed: {results}")
                concurrent_reads = False
            
            os.unlink(temp_file_path)
            
        except Exception as e:
            print(f"  ❌ Concurrent read test failed: {e}")
            concurrent_reads = False
        
        # Test file locking mechanism
        try:
            import fcntl
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                temp_file.write('{"test": "locking"}')
                temp_file_path = temp_file.name
            
            # Test exclusive lock
            with open(temp_file_path, 'r') as f:
                try:
                    fcntl.flock(f.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    print("  ✅ File locking mechanism available")
                    file_locking = True
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)
                except (OSError, AttributeError):
                    print("  ⚠️ File locking not available on this system")
                    file_locking = True  # Not necessarily a failure
            
            os.unlink(temp_file_path)
            
        except ImportError:
            print("  ⚠️ fcntl not available (Windows system)")
            file_locking = True  # Not necessarily a failure
        except Exception as e:
            print(f"  ❌ File locking test failed: {e}")
            file_locking = False
        
        # Test race condition protection
        try:
            shared_counter = {"value": 0}
            lock = threading.Lock()
            
            def increment_counter():
                for _ in range(100):
                    with lock:
                        shared_counter["value"] += 1
            
            # Run multiple threads
            threads = [threading.Thread(target=increment_counter) for _ in range(5)]
            for thread in threads:
                thread.start()
            for thread in threads:
                thread.join()
            
            # Should be exactly 500 (5 threads * 100 increments)
            if shared_counter["value"] == 500:
                print("  ✅ Race condition protection working")
                race_protection = True
            else:
                print(f"  ❌ Race condition detected: {shared_counter['value']} != 500")
                race_protection = False
                
        except Exception as e:
            print(f"  ❌ Race condition test failed: {e}")
            race_protection = False
        
        success_rate = sum([concurrent_reads, file_locking, race_protection]) / 3
        
        if success_rate >= 0.8:
            print(f"  ✅ Concurrent file access working ({success_rate:.1%})")
            return True
        else:
            print(f"  ❌ Concurrent file access insufficient ({success_rate:.1%})")
            return False
            
    except Exception as e:
        print(f"  ❌ Concurrent file access test failed: {e}")
        return False

def test_data_corruption_protection():
    """Test data corruption detection and protection"""
    print("🧪 Testing data corruption protection...")
    
    try:
        # Test checksum validation
        try:
            from src.core.data.loader import calculate_file_checksum
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                test_data = '{"test": "checksum_validation"}'
                temp_file.write(test_data)
                temp_file_path = Path(temp_file.name)
            
            # Calculate checksum
            checksum1 = calculate_file_checksum(temp_file_path)
            checksum2 = calculate_file_checksum(temp_file_path)
            
            # Should be consistent
            if checksum1 == checksum2 and len(checksum1) == 32:  # MD5 is 32 chars
                print("  ✅ Checksum validation working")
                checksum_validation = True
            else:
                print(f"  ❌ Checksum validation failed: {checksum1} vs {checksum2}")
                checksum_validation = False
            
            os.unlink(temp_file_path)
            
        except Exception as e:
            print(f"  ❌ Checksum validation test failed: {e}")
            checksum_validation = False
        
        # Test JSON integrity validation
        try:
            # Test valid JSON
            valid_json = '{"valid": true, "data": [1, 2, 3]}'
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                temp_file.write(valid_json)
                valid_json_path = temp_file.name
            
            try:
                with open(valid_json_path, 'r') as f:
                    json.load(f)
                print("  ✅ Valid JSON properly parsed")
                json_validation = True
            except json.JSONDecodeError:
                print("  ❌ Valid JSON failed to parse")
                json_validation = False
            
            os.unlink(valid_json_path)
            
            # Test invalid JSON
            invalid_json = '{"invalid": true, "data": [1, 2, 3'  # Missing closing bracket
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                temp_file.write(invalid_json)
                invalid_json_path = temp_file.name
            
            try:
                with open(invalid_json_path, 'r') as f:
                    json.load(f)
                print("  ❌ Invalid JSON not detected")
                json_validation = False
            except json.JSONDecodeError:
                print("  ✅ Invalid JSON properly detected")
                # json_validation remains True if first test passed
            
            os.unlink(invalid_json_path)
            
        except Exception as e:
            print(f"  ❌ JSON integrity test failed: {e}")
            json_validation = False
        
        # Test backup and recovery mechanisms
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create original file
                original_file = temp_path / "original.json"
                original_data = {"version": 1, "data": "important"}
                with open(original_file, 'w') as f:
                    json.dump(original_data, f)
                
                # Create backup
                backup_file = temp_path / "original.json.backup"
                shutil.copy2(original_file, backup_file)
                
                # Verify backup
                with open(backup_file, 'r') as f:
                    backup_data = json.load(f)
                
                if backup_data == original_data:
                    print("  ✅ Backup and recovery mechanism working")
                    backup_recovery = True
                else:
                    print("  ❌ Backup and recovery mechanism failed")
                    backup_recovery = False
                    
        except Exception as e:
            print(f"  ❌ Backup and recovery test failed: {e}")
            backup_recovery = False
        
        success_rate = sum([checksum_validation, json_validation, backup_recovery]) / 3
        
        if success_rate >= 0.8:
            print(f"  ✅ Data corruption protection working ({success_rate:.1%})")
            return True
        else:
            print(f"  ❌ Data corruption protection insufficient ({success_rate:.1%})")
            return False
            
    except Exception as e:
        print(f"  ❌ Data corruption protection test failed: {e}")
        return False

def test_file_locking_mechanisms():
    """Test file locking for safe concurrent access"""
    print("🧪 Testing file locking mechanisms...")
    
    try:
        # Test async file operations
        try:
            import asyncio
            
            async def async_file_operation():
                with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                    temp_file.write('{"async": "test"}')
                    temp_file_path = temp_file.name
                
                # Simulate async file reading
                await asyncio.sleep(0.01)
                
                with open(temp_file_path, 'r') as f:
                    data = json.load(f)
                
                os.unlink(temp_file_path)
                return data.get("async") == "test"
            
            result = asyncio.run(async_file_operation())
            
            if result:
                print("  ✅ Async file operations working")
                async_operations = True
            else:
                print("  ❌ Async file operations failed")
                async_operations = False
                
        except Exception as e:
            print(f"  ❌ Async file operations test failed: {e}")
            async_operations = False
        
        # Test thread-safe file access
        try:
            from src.core.async_utils import get_thread_pool
            
            def thread_file_operation(thread_id):
                with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                    temp_file.write(f'{{"thread": {thread_id}}}')
                    temp_file_path = temp_file.name
                
                time.sleep(0.01)  # Simulate work
                
                with open(temp_file_path, 'r') as f:
                    data = json.load(f)
                
                os.unlink(temp_file_path)
                return data.get("thread") == thread_id
            
            # Run multiple thread operations
            thread_pool = get_thread_pool()
            futures = [thread_pool.submit(thread_file_operation, i) for i in range(5)]
            results = [future.result() for future in futures]
            
            if all(results):
                print("  ✅ Thread-safe file access working")
                thread_safety = True
            else:
                print(f"  ❌ Thread-safe file access failed: {results}")
                thread_safety = False
                
        except Exception as e:
            print(f"  ❌ Thread-safe file access test failed: {e}")
            thread_safety = False
        
        # Test cache invalidation on file changes
        try:
            from src.core.data.loader import _data_cache, _cache_timestamp
            import src.core.data.loader as loader_module
            
            # Clear cache
            loader_module._data_cache = None
            loader_module._cache_timestamp = None
            
            # Load data (should populate cache)
            data1 = asyncio.run(loader_module.load_university_data())
            cache_time1 = loader_module._cache_timestamp
            
            # Simulate file change by setting old timestamp
            loader_module._cache_timestamp = time.time() - 400  # 400 seconds ago
            
            # Load again (should refresh cache)
            data2 = asyncio.run(loader_module.load_university_data())
            cache_time2 = loader_module._cache_timestamp
            
            if cache_time2 > cache_time1:
                print("  ✅ Cache invalidation on file changes working")
                cache_invalidation = True
            else:
                print("  ❌ Cache invalidation on file changes failed")
                cache_invalidation = False
                
        except Exception as e:
            print(f"  ❌ Cache invalidation test failed: {e}")
            cache_invalidation = False
        
        success_rate = sum([async_operations, thread_safety, cache_invalidation]) / 3
        
        if success_rate >= 0.8:
            print(f"  ✅ File locking mechanisms working ({success_rate:.1%})")
            return True
        else:
            print(f"  ❌ File locking mechanisms insufficient ({success_rate:.1%})")
            return False
            
    except Exception as e:
        print(f"  ❌ File locking mechanisms test failed: {e}")
        return False

def main():
    """Run all database and file I/O tests"""
    print("🚀 DATABASE AND FILE I/O FIXES TEST")
    print("=" * 60)
    
    tests = [
        ("File Permission Checks", test_file_permission_checks),
        ("Disk Space Handling", test_disk_space_handling),
        ("Concurrent File Access", test_concurrent_file_access),
        ("Data Corruption Protection", test_data_corruption_protection),
        ("File Locking Mechanisms", test_file_locking_mechanisms),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL DATABASE AND FILE I/O TESTS PASSED!")
        print("✅ File permission checks working")
        print("✅ Disk space handling implemented")
        print("✅ Concurrent file access protected")
        print("✅ Data corruption protection active")
        print("✅ File locking mechanisms working")
        print("\n🚀 Database and file I/O system is production ready!")
        return True
    else:
        print(f"\n⚠️ {total - passed} database/file I/O tests failed")
        print("🔧 Database and file I/O system needs fixes")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
