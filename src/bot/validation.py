"""
Input Validation and Sanitization Module
Provides security middleware for user input processing
"""

import html
import re
from typing import Optional


def sanitize_text(text: str) -> str:
    """Comprehensive text sanitization against XSS and injection attacks."""
    if text is None:
        return "None"
    text = str(text)  # Convert to string if not already

    # Remove control characters and null bytes
    text = re.sub(r"[\x00-\x1F\x7F]+", "", text)

    # Comprehensive XSS protection
    # Remove script tags and javascript: protocols
    text = re.sub(r"<script[^>]*>.*?</script>", "", text, flags=re.IGNORECASE | re.DOTALL)
    text = re.sub(r"javascript:", "", text, flags=re.IGNORECASE)

    # Remove dangerous HTML attributes
    dangerous_attrs = [
        r"on\w+\s*=",  # onclick, onload, onerror, etc.
        r"href\s*=\s*[\"']?javascript:",
        r"src\s*=\s*[\"']?javascript:",
        r"style\s*=.*?expression\s*\(",
    ]
    for attr in dangerous_attrs:
        text = re.sub(attr, "", text, flags=re.IGNORECASE)

    # Escape HTML entities
    text = html.escape(text, quote=True)

    # Telegram-MarkdownV2 reserved chars
    md_reserved = r"_*\[\]()~`>#+-=|{}.!\\"
    text = re.sub(f"([{re.escape(md_reserved)}])", r"\\\1", text)

    return text


def validate_program_id(program_id: str) -> bool:
    """
    Comprehensive program ID validation to prevent injection and traversal attacks

    Args:
        program_id: Program identifier from user input

    Returns:
        bool: True if valid, False if potentially malicious
    """
    if not isinstance(program_id, str):
        return False

    # Check for null bytes and control characters
    if any(ord(c) < 32 or ord(c) == 127 for c in program_id):
        return False

    # Check for SQL injection patterns
    sql_patterns = [
        r"['\";]",  # SQL quotes and semicolons
        r"\b(DROP|DELETE|UPDATE|INSERT|SELECT|UNION|ALTER|CREATE)\b",  # SQL keywords
        r"--",      # SQL comments
        r"/\*",     # SQL block comments
        r"\bOR\b.*?=.*?=",  # OR 1=1 patterns
    ]
    for pattern in sql_patterns:
        if re.search(pattern, program_id, re.IGNORECASE):
            return False

    # Check for command injection patterns
    cmd_patterns = [
        r"[;&|`$]",  # Command separators and substitution
        r"\$\(",     # Command substitution
        r"\.\.[\\/]", # Path traversal
    ]
    for pattern in cmd_patterns:
        if re.search(pattern, program_id):
            return False

    # Allow only alphanumeric, underscore, and hyphen, 3-60 characters
    pattern = r'^[a-z0-9_-]{3,60}$'
    return bool(re.match(pattern, program_id, re.IGNORECASE))


def validate_language_code(lang_code: str) -> bool:
    """
    Validate language code parameter
    
    Args:
        lang_code: Language code from user input
        
    Returns:
        bool: True if valid language code
    """
    if not isinstance(lang_code, str):
        return False
    
    # Only allow known language codes
    valid_codes = ['kh', 'en', 'both']
    return lang_code.lower() in valid_codes


def sanitize_command_args(message_text: str) -> list[str]:
    """
    Safely parse command arguments from message text with comprehensive security

    Args:
        message_text: Raw message text from user

    Returns:
        list[str]: Sanitized command arguments
    """
    if not isinstance(message_text, str):
        return []

    # Remove control characters and null bytes
    clean_text = ''.join(char if ord(char) >= 32 and ord(char) != 127 else ' ' for char in message_text)

    # Remove dangerous command injection patterns
    dangerous_patterns = [
        r"[;&|`$]",     # Command separators and substitution
        r"\$\(",        # Command substitution
        r"\.\.[\\/]",   # Path traversal
        r"['\";]",      # SQL injection quotes
        r"<[^>]*>",     # HTML/XML tags
    ]

    for pattern in dangerous_patterns:
        clean_text = re.sub(pattern, " ", clean_text)

    # Split and limit to reasonable number of arguments
    parts = clean_text.strip().split()[:10]  # Max 10 arguments

    # Filter out empty parts and validate each argument
    sanitized_parts = []
    for part in parts:
        if part and len(part) <= 100:  # Reasonable length limit
            # Additional validation per argument
            if not re.search(r"[^\w\-.]", part):  # Only word chars, hyphens, dots
                sanitized_parts.append(part)

    return sanitized_parts


def validate_user_input(text: str, max_length: int = 1000) -> Optional[str]:
    """
    Comprehensive user input validation with security checks

    Args:
        text: User input text
        max_length: Maximum allowed length

    Returns:
        Optional[str]: Validated text or None if invalid
    """
    if not isinstance(text, str):
        return None

    # Check length
    if len(text) > max_length:
        return None

    # Check for SQL injection patterns
    sql_patterns = [
        r"['\";].*?(DROP|DELETE|UPDATE|INSERT|SELECT|UNION|ALTER|CREATE)",
        r"\b(OR|AND)\b.*?=.*?=",
        r"--.*$",
        r"/\*.*?\*/"
    ]
    for pattern in sql_patterns:
        if re.search(pattern, text, re.IGNORECASE | re.MULTILINE):
            return None

    # Check for command injection patterns
    cmd_patterns = [
        r"[;&|`]",
        r"\$\(",
        r"\.\.[\\/]"
    ]
    for pattern in cmd_patterns:
        if re.search(pattern, text):
            return None

    # Remove null bytes and control characters except newlines and tabs
    cleaned = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')

    # Additional XSS protection
    if re.search(r"<script|javascript:|on\w+\s*=", cleaned, re.IGNORECASE):
        return None

    # Return cleaned text if not empty
    return cleaned.strip() if cleaned.strip() else None
