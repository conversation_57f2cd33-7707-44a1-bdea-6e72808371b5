"""
EduGuideBot v3 Assessment Handlers
Handles language selection and 16-question assessment flow
"""

import logging
import sys
from pathlib import Path
from telegram import Update, CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

# Use centralized imports to prevent import errors
from src.core.imports import get_keyboard_functions, get_telegram_safe_functions

# Get functions with error handling
keyboard_funcs = get_keyboard_functions()
create_assessment_keyboard = keyboard_funcs['create_assessment_keyboard']
get_question_text = keyboard_funcs['get_question_text']
get_answer_text = keyboard_funcs['get_answer_text']

safe_funcs = get_telegram_safe_functions()
safe_answer_callback = safe_funcs['safe_answer_callback']
safe_edit_message = safe_funcs['safe_edit_message']

logger = logging.getLogger(__name__)


def to_khmer_number(num: int) -> str:
    """Convert English number to Khmer number."""
    khmer_digits = {
        '0': '០', '1': '១', '2': '២', '3': '៣', '4': '៤',
        '5': '៥', '6': '៦', '7': '៧', '8': '៨', '9': '៩'
    }
    return ''.join(khmer_digits.get(digit, digit) for digit in str(num))


async def start_assessment(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Start the assessment flow with language selection."""
    try:
        # Import start command safely
        try:
            from src.bot.commands_v3 import start_command
            await start_command(update, context)
        except ImportError:
            logger.error("Could not import start_command")
            # Provide fallback behavior
            if update.callback_query:
                await safe_answer_callback(update.callback_query)
                await safe_edit_message(
                    update.callback_query,
                    "🎯 ការវាយតម្លៃ AI\n\nសូមចាប់ផ្តើមការវាយតម្លៃ។"
                )
    except Exception as e:
        logger.error(f"Error starting assessment: {e}")
        await safe_edit_message(
            update.callback_query if update.callback_query else None,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការចាប់ផ្ដើម។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_language_selection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle language selection callback - Khmer only."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        # Set Khmer as default language
        context.user_data['language'] = 'kh'

        # Show main menu with options - Khmer only
        menu_text = (
            "🎓 សូមស្វាគមន៍មកកាន់ EduGuideBot v3!\n\n"
            "ជ្រើសរើសសកម្មភាពដែលអ្នកចង់ធ្វើ:"
        )

        # Create main menu keyboard - Khmer only, simplified
        keyboard_buttons = [
            [
                InlineKeyboardButton("🎯 តេស្ត ១៦ សំណួរ", callback_data="start_assessment")
            ],
            [
                InlineKeyboardButton("❓ ជំនួយ", callback_data="show_help")
            ]
        ]

        from telegram import InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup(keyboard_buttons)

        await safe_edit_message(query, menu_text, keyboard)

        # Initialize assessment
        context.user_data['current_question'] = 0
        context.user_data['assessment_answers'] = {}

    except Exception as e:
        logger.error(f"Error in handle_language_selection: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_assessment_answer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle assessment answer callback."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    try:
        # Parse callback data: ans_question_answer
        parts = query.data.split("_")
        question_num = int(parts[1])
        answer_index = int(parts[2])
        
        # Store answer in both formats for compatibility
        answer_text = get_answer_text(question_num, answer_index)

        # Store in assessment_answers (current format)
        context.user_data['assessment_answers'][question_num] = {
            'answer_index': answer_index,
            'answer_text': answer_text
        }

        # Also store in assessment format for recommendations handler
        if 'assessment' not in context.user_data:
            context.user_data['assessment'] = {}
        context.user_data['assessment'][question_num] = {
            'answer_index': answer_index,
            'answer_text': answer_text
        }
        
        # Move to next question
        next_question = question_num + 1
        
        if next_question < 16:  # More questions
            question_text = get_question_text(next_question)
            keyboard = create_assessment_keyboard(next_question)
            
            progress_text = f"🎯 **ការវាយតម្លៃ AI** - សំណួរ {to_khmer_number(next_question + 1)}/{to_khmer_number(16)}\n\n📋 {question_text}\n\n💡 *ជ្រើសរើសចម្លើយដែលសមស្របបំផុតសម្រាប់អ្នក*"
            
            await safe_edit_message(query, progress_text, keyboard)
            context.user_data['current_question'] = next_question
            
        else:  # Assessment complete
            await complete_assessment(query, context)
            
    except Exception as e:
        logger.error(f"Error in handle_assessment_answer: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def complete_assessment(query: CallbackQuery, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Complete assessment and show recommendations."""
    try:
        # Show processing message
        await safe_edit_message(query, "⏳ កំពុងដំណើរការ...")

        # Get user answers
        answers = context.user_data.get('assessment_answers', {})
        logger.info(f"Assessment answers: {len(answers)} questions answered")
        logger.debug(f"Answer data: {answers}")

        # Import recommendation system using centralized imports
        try:
            from src.core.imports import get_recommender
            get_recommendations = get_recommender()
            # Convert answers to assessment format
            assessment = {
                'lang': context.user_data.get('lang', 'kh'),
                **answers
            }
            recommendations = await get_recommendations(assessment)
            logger.info(f"Generated {len(recommendations)} recommendations")
        except Exception as e:
            logger.error(f"Recommendation system failed: {e}")
            # Fallback to MCDA only
            try:
                from src.core.imports import get_mcda_scorer_func
                mcda_scorer = get_mcda_scorer_func()
                assessment = {
                    'lang': context.user_data.get('lang', 'kh'),
                    **answers
                }
                recommendations = mcda_scorer(assessment)
            except Exception as e2:
                logger.error(f"MCDA fallback failed: {e2}")
                recommendations = []

        if not recommendations:
            await safe_edit_message(
                query,
                "❌ សូមអភ័យទោស! រកមិនឃើញការណែនាំសម្រាប់អ្នក។ សូមព្យាយាមម្តងទៀត។"
            )
            return

        # Store recommendations in context
        context.user_data['recommendations'] = recommendations

        # Format recommendations
        rec_text = "🎯 ការណែនាំសម្រាប់អ្នក\n\n"
        rec_text += "ខាងក្រោមនេះជាមុខជំនាញដែលសមស្របបំផុតសម្រាប់អ្នក:\n\n"

        for i, rec in enumerate(recommendations[:5], 1):
            # Access fields directly from rec (no 'program' wrapper)
            name_kh = rec.get('major_name_kh', rec.get('major_name_en', 'មិនមានឈ្មោះ'))
            uni_kh = rec.get('university_name_kh', rec.get('university_name_en', 'មិនមានឈ្មោះ'))
            location = rec.get('city', rec.get('location', 'មិនមានទីតាំង'))
            fees = rec.get('tuition_fees_usd', rec.get('fees_usd', 'N/A'))

            # Get hybrid score safely
            hybrid_score = rec.get('hybrid_score', rec.get('score', 0.5))
            confidence = get_confidence_stars(hybrid_score)

            rec_text += f"{i}. {name_kh}\n"
            rec_text += f"🏫 {uni_kh}\n"
            rec_text += f"📍 {location}\n"
            if fees != 'N/A':
                rec_text += f"💰 ${fees} USD\n"
            rec_text += f"★ {confidence} ({hybrid_score * 100:.0f}% ត្រូវគ្នា)\n\n"

        # Create action buttons for each recommendation using actual major IDs
        keyboard_buttons = []
        for i, rec in enumerate(recommendations[:5]):
            major_id = rec.get('major_id', f'unknown_{i}')
            keyboard_buttons.append([
                InlineKeyboardButton(f"🔍 ព័ត៌មានបន្ថែម #{i+1}", callback_data=f"details_{major_id}")
            ])

        keyboard_buttons.append([
            InlineKeyboardButton("🔄 ធ្វើតេស្តម្តងទៀត", callback_data="restart_assessment")
        ])

        from telegram import InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup(keyboard_buttons)

        await safe_edit_message(query, rec_text, keyboard)

        # Update stats using centralized imports
        try:
            from src.core.imports import get_bot_stats
            bot_stats = get_bot_stats()
            bot_stats['total_assessments'] += 1
        except Exception as e:
            logger.error(f"Failed to update stats: {e}")

    except Exception as e:
        logger.error(f"Error in complete_assessment: {e}", exc_info=True)
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


def get_confidence_stars(score: float) -> str:
    """Convert score to star rating."""
    if score >= 0.8:
        return "★★★★★"
    elif score >= 0.6:
        return "★★★★☆"
    elif score >= 0.4:
        return "★★★☆☆"
    else:
        return "★★☆☆☆"


async def start_assessment_flow(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Start the 16-question assessment flow."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Start assessment
        assessment_text = (
            "🎯 ការវាយតម្លៃ\n\n"
            "ខ្ញុំនឹងសួរសំណួរ ១៦ ដើម្បីយល់ពីចំណង់ចំណូលចិត្ត និងគោលដៅរបស់អ្នក។\n\n"
            "តោះចាប់ផ្តើម!"
        )

        # Show first question
        question_text = get_question_text(0)
        keyboard = create_assessment_keyboard(0)

        full_text = f"{assessment_text}\n\n🎯 **ការវាយតម្លៃ AI** - សំណួរ {to_khmer_number(1)}/{to_khmer_number(16)}\n\n📋 {question_text}\n\n💡 *ជ្រើសរើសចម្លើយដែលសមស្របបំផុតសម្រាប់អ្នក*"

        await safe_edit_message(query, full_text, keyboard)

        # Initialize assessment
        context.user_data['current_question'] = 0
        context.user_data['assessment_answers'] = {}

    except Exception as e:
        logger.error(f"Error starting assessment flow: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការចាប់ផ្តើម។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_back_to_question(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle back to previous question."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Extract target question number from callback data
        # Format: back_to_question_<question_num>
        parts = query.data.split("_")
        if len(parts) >= 4:
            target_question = int(parts[3])
        else:
            target_question = 0

        # Validate question number
        if target_question < 0 or target_question >= 16:
            target_question = 0

        # Remove answers for questions after the target question
        answers = context.user_data.get('assessment_answers', {})
        assessment = context.user_data.get('assessment', {})

        # Clean up answers from future questions
        for q_num in list(answers.keys()):
            if q_num > target_question:
                del answers[q_num]
        for q_num in list(assessment.keys()):
            if q_num > target_question:
                del assessment[q_num]

        # Show the target question
        question_text = get_question_text(target_question)
        keyboard = create_assessment_keyboard(target_question)

        progress_text = f"🎯 **ការវាយតម្លៃ AI** - សំណួរ {to_khmer_number(target_question + 1)}/{to_khmer_number(16)}\n\n📋 {question_text}\n\n💡 *ជ្រើសរើសចម្លើយដែលសមស្របបំផុតសម្រាប់អ្នក*"

        await safe_edit_message(query, progress_text, keyboard)
        context.user_data['current_question'] = target_question

    except Exception as e:
        logger.error(f"Error in handle_back_to_question: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការត្រឡប់ទៅសំណួរមុន។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_back_to_main_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle back to main menu from first question."""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Clear assessment data
        context.user_data['assessment_answers'] = {}
        context.user_data['assessment'] = {}
        context.user_data['current_question'] = 0

        # Show main menu
        menu_text = (
            "🎓 សូមស្វាគមន៍មកកាន់ EduGuideBot v3!\n\n"
            "ជ្រើសរើសសកម្មភាពដែលអ្នកចង់ធ្វើ:"
        )

        keyboard_buttons = [
            [
                InlineKeyboardButton("🎯 តេស្ត ១៦ សំណួរ", callback_data="start_assessment")
            ],
            [
                InlineKeyboardButton("❓ ជំនួយ", callback_data="show_help")
            ]
        ]

        from telegram import InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup(keyboard_buttons)

        await safe_edit_message(query, menu_text, keyboard)

    except Exception as e:
        logger.error(f"Error in handle_back_to_main_menu: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការត្រឡប់ទៅមេនុយដើម។ សូមព្យាយាមម្តងទៀត។"
        )


# Create aliases for compatibility
answer_handler = handle_assessment_answer
