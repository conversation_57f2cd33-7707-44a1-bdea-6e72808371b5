"""
Bot Utilities Module
Shared helper functions for bot commands and handlers
"""

import logging
from typing import Dict, Any, List, Optional
from telegram import Update
from telegram.ext import ContextTypes

logger = logging.getLogger(__name__)


def get_program_from_cache_or_lookup(program_id: str, context: ContextTypes.DEFAULT_TYPE) -> Optional[Dict[str, Any]]:
    """
    Get program data from cache or perform fresh lookup
    
    Args:
        program_id: Program identifier
        context: Bot context containing user data
        
    Returns:
        Dict containing program data with scores, or None if not found
    """
    # First try to get from cache (last recommendations)
    cache = context.user_data.get("last_recs", {})
    programme = cache.get(program_id)
    
    if programme:
        logger.debug(f"Found program {program_id} in cache")
        return programme
    
    # Fallback to fresh lookup
    logger.debug(f"Program {program_id} not in cache, performing fresh lookup")
    try:
        from src.core.data_loader import load_raw
        from src.core.feature_engineering import add_derived_features
        
        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)
        
        # Find program by ID
        for prog in enhanced_data:
            if prog.get('major_id') == program_id:
                return prog
        
        logger.warning(f"Program {program_id} not found in data")
        return None
        
    except Exception as e:
        logger.error(f"Error loading program data: {e}")
        return None


def ensure_program_scores(programme: Dict[str, Any], user_answers: Dict[str, Any]) -> Dict[str, Any]:
    """
    Ensure program has all required scores (MCDA, ML, hybrid, reason)
    
    Args:
        programme: Program data dictionary
        user_answers: User's assessment answers
        
    Returns:
        Program dictionary with all scores populated
    """
    programme_copy = programme.copy()
    
    # Check if scores are already present
    has_mcda = 'mcda_score' in programme_copy
    has_ml = 'ml_score' in programme_copy
    has_hybrid = 'hybrid_score' in programme_copy
    has_reason = 'mcda_reason' in programme_copy
    
    if has_mcda and has_ml and has_hybrid and has_reason:
        return programme_copy
    
    # Calculate missing scores using new vectorized system
    try:
        from src.core.hybrid_recommender import get_recommendations
        
        # Get scores for this single program
        scored_programs = get_recommendations(user_answers, programmes=[programme_copy], top_k=1)
        
        if scored_programs:
            scored_program = scored_programs[0]
            
            # Update missing scores
            if not has_mcda:
                programme_copy['mcda_score'] = scored_program.get('mcda_score', 0.0)
            if not has_ml:
                programme_copy['ml_score'] = scored_program.get('ml_score', 0.0)
            if not has_hybrid:
                programme_copy['hybrid_score'] = scored_program.get('hybrid_score', 0.0)
            if not has_reason:
                programme_copy['mcda_reason'] = scored_program.get('mcda_reason', 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ')
        else:
            # Fallback values if scoring fails
            if not has_mcda:
                programme_copy['mcda_score'] = 0.0
            if not has_ml:
                programme_copy['ml_score'] = 0.0
            if not has_hybrid:
                programme_copy['hybrid_score'] = 0.0
            if not has_reason:
                programme_copy['mcda_reason'] = 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ'
                
    except Exception as e:
        logger.error(f"Error calculating scores: {e}")
        # Fallback values
        if not has_mcda:
            programme_copy['mcda_score'] = 0.0
        if not has_ml:
            programme_copy['ml_score'] = 0.0
        if not has_hybrid:
            programme_copy['hybrid_score'] = 0.0
        if not has_reason:
            programme_copy['mcda_reason'] = 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ'
    
    return programme_copy


def cache_recommendations(recommendations: List[Dict[str, Any]], context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Cache recommendations for quick access by details command
    
    Args:
        recommendations: List of recommendation dictionaries
        context: Bot context to store cache
    """
    if not recommendations:
        return
    
    # Create cache mapping program_id -> program_data
    cache = {}
    for rec in recommendations:
        program_id = rec.get('major_id')
        if program_id:
            cache[program_id] = rec
    
    # Store in user data
    if 'last_recs' not in context.user_data:
        context.user_data['last_recs'] = {}
    
    context.user_data['last_recs'].update(cache)
    logger.debug(f"Cached {len(cache)} recommendations")


def cache_user_answers(user_answers: Dict[str, Any], context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Cache user answers for scoring calculations
    
    Args:
        user_answers: User's assessment answers
        context: Bot context to store cache
    """
    context.user_data['last_answers'] = user_answers.copy()
    logger.debug(f"Cached user answers: {list(user_answers.keys())}")


def format_recommendation_card(program: Dict[str, Any], index: int) -> str:
    """
    Format a single recommendation as a card
    
    Args:
        program: Program data dictionary
        index: 1-based index for display
        
    Returns:
        Formatted card text
    """
    mcda_score = program.get('mcda_score', 0.0)
    ml_score = program.get('ml_score', 0.0)
    hybrid_score = program.get('hybrid_score', 0.0)
    reason = program.get('mcda_reason', 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ')
    
    # Use hybrid score if available, otherwise fall back to MCDA
    display_score = hybrid_score if hybrid_score > 0 else mcda_score
    
    # Format tuition fee display
    tuition_fee = program.get('tuition_fees_usd', 'N/A')
    fee_display = f"${tuition_fee} USD" if tuition_fee not in ['N/A', '', 0, None] else 'មិនមានទិន្នន័យតម្លៃសិក្សា'

    card_text = f"""🎓 {index}. {program.get('major_name_kh', 'មិនមានឈ្មោះ')}
📍 {program.get('university_name_kh', 'មិនមានឈ្មោះ')}
🏛️ {program.get('city', 'មិនមានទីតាំង')}
💰 {fee_display}
📊 MCDA: {mcda_score:.2f} | ML: {ml_score:.2f}

💡 {reason}

Type /details_{program.get('major_id', 'unknown')} for more information"""
    
    return card_text


def format_program_details(programme: Dict[str, Any]) -> str:
    """
    Format program details using the standard template
    
    Args:
        programme: Program data dictionary with scores
        
    Returns:
        Formatted details text
    """
    # Template for details display
    template = (
        "📚 {major_name_kh}\n"
        "🏛️ {university_name_kh} – {city}\n"
        "💵 Tuition: {tuition_fees_usd} USD\n"
        "📈 MCDA {mcda_score:.2f}   ML {ml_score:.2f}\n"
        "{mcda_reason}"
    )
    
    try:
        # Format tuition fee before using template
        tuition_fee = programme.get('tuition_fees_usd', 'N/A')
        fee_display = f"${tuition_fee} USD" if tuition_fee not in ['N/A', '', 0, None] else 'មិនមានទិន្នន័យតម្លៃសិក្សា'

        # Create a copy with formatted tuition
        formatted_programme = programme.copy()
        formatted_programme['tuition_fees_usd'] = fee_display

        return template.format(**formatted_programme)
    except KeyError as e:
        logger.error(f"Missing field in program data: {e}")
        # Return basic info if template fails
        # Format tuition fee for fallback display
        tuition_fee = programme.get('tuition_fees_usd', 'N/A')
        fee_display = f"${tuition_fee} USD" if tuition_fee not in ['N/A', '', 0, None] else 'មិនមានទិន្នន័យតម្លៃសិក្សា'

        return f"""📚 {programme.get('major_name_kh', 'មិនមានឈ្មោះ')}
🏛️ {programme.get('university_name_kh', 'មិនមានឈ្មោះ')} – {programme.get('city', 'មិនមានទីតាំង')}
💵 Tuition: {fee_display}
📈 MCDA {programme.get('mcda_score', 0.0):.2f}   ML {programme.get('ml_score', 0.0):.2f}
{programme.get('mcda_reason', 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ')}"""


def extract_program_id_from_command(command_text: str) -> Optional[str]:
    """
    Extract program ID from /details_<program_id> command

    Args:
        command_text: Full command text (e.g., "/details_cs-001")

    Returns:
        Program ID or None if invalid format
    """
    if not command_text or not command_text.startswith('/details_'):
        return None

    # Extract everything after /details_
    program_id = command_text[9:]  # len('/details_') = 9

    # Basic validation - should not be empty
    if not program_id:
        return None

    return program_id


def extract_program_id(text: str) -> Optional[str]:
    """
    Extract program ID from command text (alias for compatibility)

    Args:
        text: Command text

    Returns:
        Program ID or None if invalid
    """
    return extract_program_id_from_command(text)


def log_recommendation_metrics(recommendations: List[Dict[str, Any]]) -> None:
    """
    Log metrics about recommendations for monitoring
    
    Args:
        recommendations: List of recommendation dictionaries
    """
    if not recommendations:
        logger.info("No recommendations generated")
        return
    
    # Calculate average scores
    mcda_scores = [rec.get('mcda_score', 0.0) for rec in recommendations]
    ml_scores = [rec.get('ml_score', 0.0) for rec in recommendations]
    hybrid_scores = [rec.get('hybrid_score', 0.0) for rec in recommendations]
    
    avg_mcda = sum(mcda_scores) / len(mcda_scores) if mcda_scores else 0.0
    avg_ml = sum(ml_scores) / len(ml_scores) if ml_scores else 0.0
    avg_hybrid = sum(hybrid_scores) / len(hybrid_scores) if hybrid_scores else 0.0
    
    logger.info(f"Generated {len(recommendations)} recommendations")
    logger.info(f"Average scores - MCDA: {avg_mcda:.3f}, ML: {avg_ml:.3f}, Hybrid: {avg_hybrid:.3f}")
    
    # Log top recommendation details
    top_rec = recommendations[0]
    logger.info(f"Top recommendation: {top_rec.get('major_id')} (hybrid: {top_rec.get('hybrid_score', 0.0):.3f})")


def validate_user_answers(user_answers: Dict[str, Any]) -> bool:
    """
    Validate that user answers contain minimum required fields
    
    Args:
        user_answers: User's assessment answers
        
    Returns:
        True if valid, False otherwise
    """
    if not user_answers:
        return False
    
    # Check for at least some key fields
    required_fields = ['location_preference', 'budget_range', 'field_of_interest']
    
    # At least one required field should be present
    return any(field in user_answers for field in required_fields)
