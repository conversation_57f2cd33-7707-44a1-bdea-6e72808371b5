"""
EduGuideBot v3 Data Loader
Loads and processes university data from JSON files
"""

import json
import logging
import os
import stat
from pathlib import Path
from typing import List, Dict, Any, Optional
import asyncio
import time
import hashlib

logger = logging.getLogger(__name__)

# Cache for loaded data
_data_cache = None
_cache_timestamp = None
_file_checksums = {}

# Data validation constants
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB max file size
MIN_MAJORS_PER_FILE = 1
MAX_MAJORS_PER_FILE = 1000
REQUIRED_UNIVERSITY_FIELDS = ['name_kh', 'name_en']
REQUIRED_MAJOR_FIELDS = ['name_kh', 'name_en']


def validate_file_access(file_path: Path) -> bool:
    """Validate file exists and is readable."""
    try:
        if not file_path.exists():
            logger.error(f"File does not exist: {file_path}")
            return False

        if not file_path.is_file():
            logger.error(f"Path is not a file: {file_path}")
            return False

        # Check file size
        file_size = file_path.stat().st_size
        if file_size > MAX_FILE_SIZE:
            logger.error(f"File too large ({file_size} bytes): {file_path}")
            return False

        if file_size == 0:
            logger.error(f"File is empty: {file_path}")
            return False

        # Check read permissions
        if not os.access(file_path, os.R_OK):
            logger.error(f"File not readable: {file_path}")
            return False

        return True

    except Exception as e:
        logger.error(f"Error validating file access for {file_path}: {e}")
        return False


def validate_json_structure(data: Dict[str, Any], file_path: Path) -> bool:
    """Validate JSON structure and required fields."""
    try:
        # Check if data is a dictionary
        if not isinstance(data, dict):
            logger.error(f"JSON root is not a dictionary in {file_path}")
            return False

        # Check for university section
        university = data.get('university', {})
        if not isinstance(university, dict):
            logger.error(f"University section missing or invalid in {file_path}")
            return False

        # Validate required university fields
        for field in REQUIRED_UNIVERSITY_FIELDS:
            if not university.get(field):
                logger.warning(f"Missing university field '{field}' in {file_path}")

        # Check for majors section
        majors = data.get('majors', [])
        if not isinstance(majors, list):
            logger.error(f"Majors section missing or not a list in {file_path}")
            return False

        if len(majors) < MIN_MAJORS_PER_FILE:
            logger.error(f"Too few majors ({len(majors)}) in {file_path}")
            return False  # Make this a hard failure

        if len(majors) > MAX_MAJORS_PER_FILE:
            logger.error(f"Too many majors ({len(majors)}) in {file_path}")
            return False

        # Validate each major
        for i, major in enumerate(majors):
            if not isinstance(major, dict):
                logger.error(f"Major {i} is not a dictionary in {file_path}")
                return False

            # Check required major fields
            for field in REQUIRED_MAJOR_FIELDS:
                if not major.get(field):
                    logger.warning(f"Missing major field '{field}' for major {i} in {file_path}")

        return True

    except Exception as e:
        logger.error(f"Error validating JSON structure for {file_path}: {e}")
        return False


def validate_unicode_encoding(file_path: Path) -> bool:
    """Validate file has proper Unicode encoding."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for common encoding issues
        if '\ufffd' in content:  # Unicode replacement character
            logger.error(f"Unicode encoding issues detected in {file_path}")
            return False

        # Try to encode/decode to verify
        content.encode('utf-8').decode('utf-8')

        return True

    except UnicodeDecodeError as e:
        logger.error(f"Unicode decode error in {file_path}: {e}")
        return False
    except Exception as e:
        logger.error(f"Error validating Unicode encoding for {file_path}: {e}")
        return False


def calculate_file_checksum(file_path: Path) -> str:
    """Calculate MD5 checksum of file."""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating checksum for {file_path}: {e}")
        return ""


async def load_university_data() -> List[Dict[str, Any]]:
    """
    Load university data from JSON files using enhanced loader.

    Returns:
        List of major dictionaries with university information
    """
    global _data_cache, _cache_timestamp

    try:
        # Check cache (valid for 5 minutes)
        current_time = time.time()
        if _data_cache and _cache_timestamp and (current_time - _cache_timestamp) < 300:
            logger.debug(f"Using cached data (age: {current_time - _cache_timestamp:.1f}s)")
            return _data_cache

        if _data_cache and _cache_timestamp:
            logger.info(f"Cache expired (age: {current_time - _cache_timestamp:.1f}s), refreshing data")

        # Use enhanced data loader
        from ..enhanced_data_loader import load_raw
        all_programs = load_raw()

        # Cache the data
        _data_cache = all_programs
        _cache_timestamp = current_time

        # Comprehensive logging
        logger.info(f"Enhanced data loading complete:")
        logger.info(f"  • Total programs loaded: {len(all_programs)}")

        # Validate final dataset
        if len(all_programs) == 0:
            logger.error("No programs loaded - this is a critical error!")
        elif len(all_programs) < 100:
            logger.warning(f"Only {len(all_programs)} programs loaded - this seems low")
        else:
            logger.info(f"✅ Successfully loaded {len(all_programs)} programs")

        return all_programs

    except Exception as e:
        logger.error(f"Error loading university data: {e}")
        # Fallback to empty list
        return []


async def load_university_file(file_path: Path, city_name: str) -> List[Dict[str, Any]]:
    """
    Load and process a single university JSON file.
    
    Args:
        file_path: Path to the JSON file
        city_name: Name of the city (PP, SR, BTB)
        
    Returns:
        List of processed major dictionaries
    """
    try:
        # Load and validate JSON
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in {file_path}: {e}")
                return []

        # Validate JSON structure
        if not validate_json_structure(data, file_path):
            logger.error(f"Invalid JSON structure in {file_path}")
            return []

        # Extract university information
        university_info = data.get('university', {})
        majors_data = data.get('majors', [])

        if not majors_data:
            logger.warning(f"No majors found in {file_path}")
            return []

        # Additional validation for empty dataset
        if len(majors_data) == 0:
            logger.warning(f"Empty majors list in {file_path}")
            return []
        
        processed_majors = []
        
        for i, major in enumerate(majors_data):
            try:
                # Validate individual major data
                if not isinstance(major, dict):
                    logger.error(f"Major {i} is not a dictionary in {file_path}")
                    continue

                # Check for completely empty major
                if not any(major.values()):
                    logger.warning(f"Major {i} is empty in {file_path}")
                    continue

                processed_major = process_major_data(major, university_info, city_name)
                if processed_major:
                    processed_majors.append(processed_major)
                else:
                    logger.warning(f"Failed to process major {i} in {file_path}")

            except Exception as e:
                logger.error(f"Error processing major {i} in {file_path}: {e}")
                continue
        
        logger.debug(f"Processed {len(processed_majors)} majors from {file_path}")
        return processed_majors
        
    except Exception as e:
        logger.error(f"Error loading file {file_path}: {e}")
        return []


def process_major_data(major: Dict[str, Any], university: Dict[str, Any], city_name: str) -> Dict[str, Any]:
    """
    Process and standardize major data with comprehensive validation.

    Args:
        major: Raw major data from JSON
        university: University information
        city_name: City code (PP, SR, BTB)

    Returns:
        Processed major dictionary or None if validation fails
    """
    try:
        # Validate input parameters
        if not isinstance(major, dict):
            logger.error("Major data is not a dictionary")
            return None

        if not isinstance(university, dict):
            logger.error("University data is not a dictionary")
            return None

        if not city_name or not isinstance(city_name, str):
            logger.error("Invalid city name")
            return None

        # Check for required fields
        major_name_kh = major.get('name_kh', '').strip()
        major_name_en = major.get('name_en', '').strip()
        university_name_kh = university.get('name_kh', '').strip()
        university_name_en = university.get('name_en', '').strip()

        if not major_name_kh and not major_name_en:
            logger.warning("Major missing both Khmer and English names")
            return None

        if not university_name_kh and not university_name_en:
            logger.warning("University missing both Khmer and English names")
            return None
        # Map city codes to full names
        city_map = {
            'PP': 'Phnom Penh',
            'SR': 'Siem Reap',
            'BTB': 'Battambang'
        }
        
        city_full_name = city_map.get(city_name, city_name)
        
        # Generate unique ID
        university_name = university.get('name_en', university.get('name', ''))
        major_name = major.get('name_en', major.get('name', ''))
        major_id = f"{city_name}_{university_name}_{major_name}".replace(' ', '_').lower()
        
        # Extract and standardize fees
        fees_usd = extract_fees(major.get('tuition_fees', major.get('fees', '')))
        
        # Extract duration
        duration = extract_duration(major.get('duration', ''))
        
        # Extract employment rate
        employment_rate = extract_employment_rate(major.get('employment_rate', ''))
        
        processed_major = {
            # Identifiers
            'id': major_id,
            'university_id': f"{city_name}_{university_name}".replace(' ', '_').lower(),
            
            # Names (Khmer and English)
            'major_name_kh': major.get('name_kh', major.get('name', '')),
            'major_name_en': major.get('name_en', major.get('name', '')),
            'university_name_kh': university.get('name_kh', university.get('name', '')),
            'university_name_en': university.get('name_en', university.get('name', '')),
            
            # Location
            'city': city_full_name,
            'city_code': city_name,
            
            # Financial information
            'tuition_fees_usd': fees_usd,
            'tuition_fees_original': major.get('tuition_fees', major.get('fees', '')),
            
            # Academic information
            'duration_years': duration,
            'employment_rate': employment_rate,
            'category': major.get('category', ''),
            'degree_level': major.get('degree_level', major.get('level', '')),
            
            # Descriptions
            'description_kh': major.get('description_kh', ''),
            'description_en': major.get('description_en', major.get('description', '')),
            'career_prospects_kh': major.get('career_prospects_kh', ''),
            'career_prospects_en': major.get('career_prospects_en', ''),
            
            # Requirements and subjects
            'requirements': major.get('requirements', []),
            'subjects': major.get('subjects', []),
            'prerequisites': major.get('prerequisites', []),
            
            # Additional information
            'language_of_instruction': major.get('language_of_instruction', []),
            'learning_modes': major.get('learning_modes', ['in_person']),
            'internship_availability': major.get('internship_availability', ''),
            
            # University contact information
            'university_address': university.get('address', ''),
            'university_phone': university.get('phone', ''),
            'university_email': university.get('email', ''),
            'university_website': university.get('website', ''),
            'university_facebook': university.get('facebook', ''),
            'campus_info': university.get('campus_info', ''),
            
            # Raw data for reference
            'raw_major': major,
            'raw_university': university
        }
        
        return processed_major
        
    except Exception as e:
        logger.error(f"Error processing major data: {e}")
        return None


def extract_fees(fees_str: str) -> float:
    """Extract numerical fees from string."""
    try:
        if not fees_str:
            return 1000.0  # Default assumption
        
        # Remove common currency symbols and text
        fees_clean = str(fees_str).replace('$', '').replace('USD', '').replace(',', '').strip()
        
        # Extract numbers
        import re
        numbers = re.findall(r'\d+\.?\d*', fees_clean)
        
        if numbers:
            return float(numbers[0])
        
        return 1000.0  # Default
        
    except Exception as e:
        logger.debug(f"Error extracting fees from '{fees_str}': {e}")
        return 1000.0


def extract_duration(duration_str: str) -> int:
    """Extract duration in years from string."""
    try:
        if not duration_str:
            return 4  # Default
        
        # Extract numbers
        import re
        numbers = re.findall(r'\d+', str(duration_str))
        
        if numbers:
            duration = int(numbers[0])
            # Assume reasonable range
            if 1 <= duration <= 8:
                return duration
        
        return 4  # Default
        
    except Exception as e:
        logger.debug(f"Error extracting duration from '{duration_str}': {e}")
        return 4


def extract_employment_rate(rate_str: str) -> float:
    """Extract employment rate percentage from string."""
    try:
        if not rate_str:
            return 70.0  # Default assumption
        
        # Extract numbers
        import re
        numbers = re.findall(r'\d+\.?\d*', str(rate_str))
        
        if numbers:
            rate = float(numbers[0])
            # Ensure reasonable range
            if 0 <= rate <= 100:
                return rate
            elif rate > 100:
                return rate / 10  # Might be in wrong format
        
        return 70.0  # Default
        
    except Exception as e:
        logger.debug(f"Error extracting employment rate from '{rate_str}': {e}")
        return 70.0


async def get_major_details(major_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific major."""
    try:
        university_data = await load_university_data()
        
        for major in university_data:
            if major.get('id') == major_id:
                return major
        
        logger.warning(f"Major not found: {major_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error getting major details: {e}")
        return None


async def get_university_details(university_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific university."""
    try:
        university_data = await load_university_data()

        # Find majors from this university
        university_majors = [
            major for major in university_data
            if major.get('university_id') == university_id
        ]

        if not university_majors:
            logger.warning(f"University not found: {university_id}")
            return None

        # Use first major to get university info
        first_major = university_majors[0]

        return {
            'id': university_id,
            'name': first_major.get('university_name_kh', first_major.get('university_name_en', 'មិនមានឈ្មោះ')),
            'city': first_major.get('city', 'មិនមានទីតាំង'),
            'address': first_major.get('university_address', 'មិនមានអាសយដ្ឋាន'),
            'phone': first_major.get('university_phone', ''),
            'email': first_major.get('university_email', ''),
            'website': first_major.get('university_website', ''),
            'facebook': first_major.get('university_facebook', ''),
            'campus_info': first_major.get('campus_info', ''),
            'majors': [
                {
                    'id': major.get('id', ''),
                    'name': major.get('major_name_kh', major.get('major_name_en', 'មិនមានឈ្មោះ')),
                    'fees_usd': major.get('tuition_fees_usd', 'N/A')
                }
                for major in university_majors
            ]
        }

    except Exception as e:
        logger.error(f"Error getting university details: {e}")
        return None


def get_university_by_id(university_id: str) -> Dict[str, Any]:
    """Synchronous version of get_university_details for compatibility."""
    try:
        # Try to get from cache first
        if _data_cache:
            university_majors = [
                major for major in _data_cache
                if major.get('university_id') == university_id
            ]

            if university_majors:
                first_major = university_majors[0]
                raw_university = first_major.get('raw_university', {})

                return {
                    'university': {
                        'name_kh': first_major.get('university_name_kh', 'មិនទាន់មានទិន្នន័យ'),
                        'name_en': first_major.get('university_name_en', 'No data'),
                        'location': {
                            'city': first_major.get('city', 'មិនទាន់មានទិន្នន័យ'),
                            'address_kh': first_major.get('university_address', 'មិនទាន់មានទិន្នន័យ'),
                            'country': 'កម្ពុជា'
                        },
                        'contact': {
                            'phone': [first_major.get('university_phone')] if first_major.get('university_phone') else ['មិនទាន់មានទិន្នន័យ'],
                            'email': first_major.get('university_email', 'មិនទាន់មានទិន្នន័យ'),
                            'website': first_major.get('university_website', 'មិនទាន់មានទិន្នន័យ'),
                            'social_media': {
                                'facebook': first_major.get('university_facebook', ''),
                                'youtube': '',
                                'telegram': ''
                            }
                        }
                    }
                }

        logger.warning(f"University not found in cache: {university_id}")
        return None

    except Exception as e:
        logger.error(f"Error getting university by ID: {e}")
        return None


def load_programs() -> List[Dict[str, Any]]:
    """Synchronous function to load programs for compatibility."""
    try:
        if _data_cache:
            return _data_cache

        # If no cache, try to load synchronously (not recommended for production)
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(load_university_data())
        except RuntimeError:
            # No event loop running, create a new one
            return asyncio.run(load_university_data())

    except Exception as e:
        logger.error(f"Error loading programs: {e}")
        return []
