"""
Performance Optimizer for EduGuideBot
Provides optimized algorithms and caching for better performance.
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional
from functools import lru_cache
import numpy as np

logger = logging.getLogger(__name__)

# Performance monitoring
_performance_metrics = {}
_metrics_lock = threading.Lock()

# Cache settings
CACHE_TTL = 300  # 5 minutes
MAX_CACHE_SIZE = 1000

class PerformanceMonitor:
    """Monitor and track performance metrics."""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self._record_metric(self.operation_name, duration)
    
    def _record_metric(self, operation: str, duration: float):
        """Record performance metric."""
        with _metrics_lock:
            if operation not in _performance_metrics:
                _performance_metrics[operation] = []
            
            # Keep only last 100 measurements
            metrics = _performance_metrics[operation]
            metrics.append(duration)
            if len(metrics) > 100:
                metrics.pop(0)

def get_performance_stats() -> Dict[str, Dict[str, float]]:
    """Get performance statistics."""
    with _metrics_lock:
        stats = {}
        for operation, durations in _performance_metrics.items():
            if durations:
                stats[operation] = {
                    'avg': sum(durations) / len(durations),
                    'min': min(durations),
                    'max': max(durations),
                    'count': len(durations)
                }
        return stats

class OptimizedMLScorer:
    """Optimized ML scoring with batch processing."""
    
    def __init__(self):
        self._model = None
        self._scaler = None
        self._model_loaded = False
    
    def _load_model_if_needed(self):
        """Lazy load ML model."""
        if self._model_loaded:
            return
        
        try:
            from .ml_reranker import MLReranker
            from pathlib import Path
            
            model_path = Path(__file__).parents[2] / "models" / "ml_reranker.pkl"
            if model_path.exists():
                ml_reranker = MLReranker(model_path)
                self._model = ml_reranker.model
                self._scaler = ml_reranker.scaler
                logger.info("ML model loaded successfully")
            else:
                logger.warning(f"ML model not found at {model_path}")
        except Exception as e:
            logger.error(f"Failed to load ML model: {e}")
        
        self._model_loaded = True
    
    def score_batch(self, user_answers: Dict[str, Any], programmes: List[Dict[str, Any]]) -> np.ndarray:
        """Score all programmes in a single batch for better performance."""
        with PerformanceMonitor("ml_batch_scoring"):
            self._load_model_if_needed()
            
            if not self._model or not self._scaler:
                # Return neutral scores if model not available
                return np.full(len(programmes), 0.5)
            
            try:
                # Extract features for all programmes at once
                features_batch = []
                for programme in programmes:
                    try:
                        from .ml_reranker import extract_features
                        features = extract_features(user_answers, programme)
                        features_batch.append(features)
                    except Exception:
                        # Use default features for failed extractions
                        features_batch.append([0.5] * 10)  # Assuming 10 features
                
                # Convert to numpy array for batch processing
                features_array = np.array(features_batch)
                
                # Scale features in batch
                features_scaled = self._scaler.transform(features_array)
                
                # Predict in batch (much faster than individual predictions)
                predictions = self._model.predict(features_scaled)
                
                # Ensure scores are in 0-1 range
                ml_scores = np.clip(predictions, 0.0, 1.0)
                
                logger.debug(f"Batch ML scoring completed for {len(programmes)} programmes")
                return ml_scores
                
            except Exception as e:
                logger.error(f"Batch ML scoring failed: {e}")
                return np.full(len(programmes), 0.5)

# Global optimized scorer instance
_optimized_scorer = OptimizedMLScorer()

def get_optimized_ml_scores(user_answers: Dict[str, Any], programmes: List[Dict[str, Any]]) -> np.ndarray:
    """Get ML scores using optimized batch processing."""
    return _optimized_scorer.score_batch(user_answers, programmes)

class FastCache:
    """Fast in-memory cache with TTL support."""
    
    def __init__(self, max_size: int = MAX_CACHE_SIZE, ttl: int = CACHE_TTL):
        self.max_size = max_size
        self.ttl = ttl
        self._cache = {}
        self._timestamps = {}
        self._lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            if key not in self._cache:
                return None
            
            # Check if expired
            if time.time() - self._timestamps[key] > self.ttl:
                del self._cache[key]
                del self._timestamps[key]
                return None
            
            return self._cache[key]
    
    def set(self, key: str, value: Any) -> None:
        """Set value in cache."""
        with self._lock:
            # Remove oldest items if cache is full
            if len(self._cache) >= self.max_size:
                oldest_key = min(self._timestamps.keys(), key=lambda k: self._timestamps[k])
                del self._cache[oldest_key]
                del self._timestamps[oldest_key]
            
            self._cache[key] = value
            self._timestamps[key] = time.time()
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
    
    def size(self) -> int:
        """Get current cache size."""
        with self._lock:
            return len(self._cache)

# Global fast cache instance
_fast_cache = FastCache()

def cache_recommendations(key: str, recommendations: List[Dict[str, Any]]) -> None:
    """Cache recommendations with fast cache."""
    _fast_cache.set(key, recommendations)

def get_cached_recommendations(key: str) -> Optional[List[Dict[str, Any]]]:
    """Get cached recommendations."""
    return _fast_cache.get(key)

def clear_recommendation_cache() -> None:
    """Clear recommendation cache."""
    _fast_cache.clear()

def get_user_cache_key(user_answers: Dict[str, Any]) -> str:
    """Generate cache key for user answers."""
    # Convert dict to sorted string for consistent hashing
    if not user_answers:
        return "empty_answers"

    # Create a deterministic string representation
    items = []
    for key, value in sorted(user_answers.items()):
        # Handle unhashable types
        if isinstance(value, (dict, list)):
            value_str = str(sorted(value.items())) if isinstance(value, dict) else str(sorted(value))
        else:
            value_str = str(value)
        items.append(f"{key}:{value_str}")

    return "_".join(items)[:100]  # Limit length

def optimize_data_loading():
    """Optimize data loading performance."""
    try:
        # Clear any stale caches
        from src.core.data.loader import _data_cache
        import src.core.data.loader as loader_module
        
        # Force cache refresh if data is stale
        current_time = time.time()
        if (loader_module._cache_timestamp and 
            current_time - loader_module._cache_timestamp > CACHE_TTL):
            logger.info("Refreshing stale data cache")
            loader_module._data_cache = None
            loader_module._cache_timestamp = None
    
    except Exception as e:
        logger.error(f"Error optimizing data loading: {e}")

def optimize_memory_usage():
    """Optimize memory usage by cleaning up caches."""
    try:
        import gc
        import sys

        # Get initial memory info
        initial_objects = len(gc.get_objects())

        # Clear fast cache
        _fast_cache.clear()

        # Clear data loader cache
        try:
            import src.core.data.loader as loader_module
            loader_module._data_cache = None
            loader_module._cache_timestamp = None
        except ImportError:
            pass

        # Force garbage collection multiple times for better cleanup
        collected = 0
        for _ in range(3):
            collected += gc.collect()

        final_objects = len(gc.get_objects())
        objects_freed = initial_objects - final_objects

        logger.info(f"Memory optimization completed: {collected} objects collected, {objects_freed} objects freed")

        return {
            'objects_collected': collected,
            'objects_freed': objects_freed,
            'initial_objects': initial_objects,
            'final_objects': final_objects
        }

    except Exception as e:
        logger.error(f"Error optimizing memory: {e}")
        return {'error': str(e)}

def get_optimization_stats() -> Dict[str, Any]:
    """Get optimization statistics."""
    return {
        'performance_metrics': get_performance_stats(),
        'cache_size': _fast_cache.size(),
        'cache_max_size': _fast_cache.max_size,
        'cache_ttl': _fast_cache.ttl
    }

# Timeout decorator for preventing infinite loops
def timeout_after(seconds: int):
    """Decorator to timeout function calls."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError(f"Function {func.__name__} timed out after {seconds} seconds")
            
            # Set timeout
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(seconds)
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # Reset alarm
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator

# Fast validation for preventing infinite loops
def validate_user_answers_fast(user_answers: Dict[str, Any]) -> bool:
    """Fast validation of user answers to prevent infinite loops."""
    if not user_answers:
        return False
    
    # Check for basic required fields
    required_fields = ['location', 'budget', 'interest_field']
    for field in required_fields:
        if field not in user_answers:
            return False
    
    # Check for reasonable values
    budget = user_answers.get('budget')
    if budget is not None:
        try:
            budget_val = float(budget)
            if budget_val < 0 or budget_val > 100000:  # Reasonable budget range
                return False
        except (ValueError, TypeError):
            return False
    
    return True
