"""
Enhanced Data Loader Module
Handles multiple data structure formats and provides robust error handling
"""

import json
import os
import logging
from functools import lru_cache
from pathlib import Path
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

@lru_cache(maxsize=1)
def load_raw(data_dir: str = None) -> List[Dict[str, Any]]:
    """
    Load all raw university JSON files with enhanced error handling
    
    Args:
        data_dir: Optional custom data directory path (for testing)
    
    Returns:
        List[Dict]: Combined list of all university programs
    """
    # Get project root directory (2 levels up from this file)
    root = Path(__file__).parents[2]
    all_programs = []
    
    # Use custom data directory if provided (for testing)
    if data_dir:
        search_path = Path(data_dir)
        json_files = list(search_path.glob("**/*.json"))
    else:
        # Recursively find all .json files in default location
        json_files = list(root.glob("data/raw/**/*.json"))
    
    logger.info(f"Found {len(json_files)} JSON files to process")
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            # Handle string data (should not happen but be safe)
            if isinstance(file_data, str):
                logger.error(f"File {json_file} contains string instead of JSON object")
                continue
            
            if not isinstance(file_data, dict):
                logger.error(f"File {json_file} does not contain a valid JSON object")
                continue
            
            # Extract university info with multiple format support
            university = extract_university_info(file_data, json_file)
            
            # Extract majors with multiple format support
            majors = extract_majors_info(file_data, json_file)
            
            if not majors:
                logger.warning(f"No valid majors found in {json_file}")
                continue
            
            # Extract location and contact info
            location = extract_location_info(file_data)
            contact = extract_contact_info(file_data)
            
            # Combine university info with each major
            for major in majors:
                program = create_program_dict(university, major, location, contact, json_file)
                
                if validate_program_data(program):
                    all_programs.append(program)
                else:
                    logger.warning(f"Invalid program data in {json_file}: {major.get('name_kh', 'Unknown')}")
        
        except Exception as e:
            logger.error(f"Error loading {json_file}: {e}")
            continue
    
    logger.info(f"Successfully loaded {len(all_programs)} programs from {len(json_files)} files")
    return all_programs

def extract_university_info(file_data: Dict[str, Any], json_file: Path) -> Dict[str, Any]:
    """Extract university information from various data formats - preserving original names exactly"""
    university = {}

    # Try different university info locations
    if 'university' in file_data and isinstance(file_data['university'], dict):
        university = file_data['university'].copy()
    elif 'university_info' in file_data and isinstance(file_data['university_info'], dict):
        university = file_data['university_info'].copy()
    else:
        # Create from root level data
        university = {
            'id': json_file.stem,
            'name_kh': file_data.get('name_kh', ''),
            'name_en': file_data.get('name_en', ''),
            'type': file_data.get('type', 'ឯកជន'),
            'city': 'Phnom Penh'  # Default
        }

    # Ensure required fields but preserve original names exactly
    if 'id' not in university:
        university['id'] = json_file.stem

    # IMPORTANT: Preserve original names exactly as they are in the data
    # Do NOT translate or modify university names
    original_name_kh = university.get('name_kh', '')
    original_name_en = university.get('name_en', '')

    # Only provide fallbacks if completely missing
    if not original_name_kh and not original_name_en:
        university['name_kh'] = 'សាកលវិទ្យាល័យ'
        university['name_en'] = 'University'
    elif not original_name_kh:
        # If only Khmer is missing, keep English as-is and leave Khmer empty
        university['name_kh'] = ''
    elif not original_name_en:
        # If only English is missing, keep Khmer as-is and leave English empty
        university['name_en'] = ''

    if 'city' not in university:
        university['city'] = 'Phnom Penh'

    return university

def extract_majors_info(file_data: Dict[str, Any], json_file: Path) -> List[Dict[str, Any]]:
    """Extract majors information with enhanced error handling"""
    if 'majors' not in file_data:
        return []
    
    majors_data = file_data['majors']
    if not isinstance(majors_data, list):
        logger.error(f"Majors in {json_file} is not a list")
        return []
    
    valid_majors = []
    for i, major in enumerate(majors_data):
        if not isinstance(major, dict):
            logger.error(f"Major {i} in {json_file} is not a dictionary")
            continue
        
        # Ensure name_kh and name_en exist
        if 'name_kh' not in major or 'name_en' not in major:
            # Try to get from major_info
            if 'major_info' in major and isinstance(major['major_info'], dict):
                major_info = major['major_info']
                if 'name_kh' in major_info:
                    major['name_kh'] = major_info['name_kh']
                if 'name_en' in major_info:
                    major['name_en'] = major_info['name_en']
        
        # If still missing, skip this major
        if 'name_kh' not in major or 'name_en' not in major:
            logger.error(f"Major {i} in {json_file} missing name_kh or name_en")
            continue
        
        valid_majors.append(major)
    
    return valid_majors

def extract_location_info(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract location information"""
    location = {}
    if 'location' in file_data and isinstance(file_data['location'], dict):
        location = file_data['location']
    return location

def extract_contact_info(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract contact information"""
    contact = {}
    if 'contact' in file_data and isinstance(file_data['contact'], dict):
        contact = file_data['contact']
    return contact

def create_program_dict(university: Dict[str, Any], major: Dict[str, Any],
                       location: Dict[str, Any], contact: Dict[str, Any],
                       json_file: Path) -> Dict[str, Any]:
    """Create a standardized program dictionary - preserving original names exactly"""

    def safe_get(obj, key, default=''):
        """Safely get value from dict or return default"""
        if isinstance(obj, dict):
            return obj.get(key, default)
        return default

    # Extract original university names exactly as they are
    uni_name_kh = safe_get(university, 'name_kh', '')
    uni_name_en = safe_get(university, 'name_en', '')

    # Extract original major names exactly as they are
    major_name_kh = safe_get(major, 'name_kh', '')
    major_name_en = safe_get(major, 'name_en', '')

    return {
        # University information - preserve original names exactly
        'university_id': safe_get(university, 'id', json_file.stem),
        'university_name_kh': uni_name_kh,  # Preserve exactly as in original data
        'university_name_en': uni_name_en,  # Preserve exactly as in original data
        'university_type': safe_get(university, 'type', 'ឯកជន'),
        'city': safe_get(university, 'city', safe_get(location, 'city', 'Phnom Penh')),
        'website': safe_get(university, 'website', safe_get(contact, 'website', '')),
        'phone': safe_get(university, 'phone', safe_get(contact, 'phone', [''])[0] if isinstance(safe_get(contact, 'phone'), list) else ''),
        'email': safe_get(university, 'email', safe_get(contact, 'email', '')),
        'address': safe_get(location, 'address_kh', safe_get(university, 'location', '')),

        # Major information - preserve original names exactly
        'major_id': safe_get(major, 'id', f"{json_file.stem}_{major_name_en.lower().replace(' ', '_') if major_name_en else 'program'}"),
        'major_name_kh': major_name_kh,  # Preserve exactly as in original data
        'major_name_en': major_name_en,  # Preserve exactly as in original data
        'degree_level': safe_get(major, 'degree_level_kh', safe_get(major.get('major_info', {}), 'degree_level_kh', 'បរិញ្ញាបត្រ')),
        'study_duration': safe_get(major, 'study_duration_kh', safe_get(major.get('major_info', {}), 'study_duration_kh', '៤ ឆ្នាំ')),
        'language_of_instruction': safe_get(major, 'language_of_instruction', safe_get(major.get('major_info', {}), 'language_of_instruction', ['ខ្មែរ'])),
        
        # Financial information
        'tuition_fees_usd': safe_get(major.get('practical_information', {}), 'tuition_fees_usd', ''),
        'tuition_fees_khr': safe_get(major.get('practical_information', {}), 'tuition_fees_khr', ''),
        'scholarship_availability': safe_get(major.get('practical_information', {}), 'scholarship_availability', False),
        
        # Career information
        'potential_careers_kh': safe_get(major.get('career_prospects', {}), 'potential_careers_kh', []),
        'potential_careers_en': safe_get(major.get('career_prospects', {}), 'potential_careers_en', []),
        'employment_rate': safe_get(major.get('career_prospects', {}).get('employment_statistics', {}), 'employment_rate', ''),
        'average_starting_salary': safe_get(major.get('career_prospects', {}).get('employment_statistics', {}), 'average_starting_salary', ''),
        
        # Academic information
        'total_credits': safe_get(major.get('program_details', {}), 'total_credits', 0) or 0,
        'minimum_gpa': safe_get(major.get('academic_requirements', {}), 'minimum_gpa', 0.0) or 0.0,
        
        # Source tracking
        'source_file': str(json_file),
    }

def validate_program_data(program: Dict[str, Any]) -> bool:
    """Validate that a program dict has required fields"""
    if not isinstance(program, dict):
        return False
    
    required_fields = [
        'university_name_kh', 'university_name_en',
        'major_name_kh', 'major_name_en'
    ]
    
    for field in required_fields:
        if field not in program or not program[field]:
            return False
    
    return True
