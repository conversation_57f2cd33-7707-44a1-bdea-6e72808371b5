# EduGuideBot v3 MCDA

from .scorer import calculate_mcda_score, generate_reason, score_vectorized

# Import from parent mcda.py for backward compatibility
try:
    from ..mcda import load_weights
except ImportError:
    # Fallback if mcda.py doesn't exist
    def load_weights():
        return {
            'question_weights': {
                'location_preference': 4.5,
                'budget_range': 5.0,
                'field_of_interest': 4.8,
                'career_goal': 4.5,
            },
            'field_weights': {'STEM': 1.0, 'Business': 1.0, 'Other': 0.8},
            'location_weights': {'ភ្នំពេញ': 1.0, 'other': 0.7},
            'budget_weights': {'Low': 1.0, 'Medium': 1.0, 'High': 1.0},
            'scholarship_bonus': 0.3,
            'employment_bonus_multiplier': 0.2
        }

__all__ = ['calculate_mcda_score', 'score_vectorized', 'generate_reason', 'load_weights']
