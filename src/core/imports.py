"""
Centralized Import Module for EduGuideBot
Provides consistent imports across all modules to prevent import errors.
"""

import logging
from typing import List, Dict, Any, Optional, Callable

logger = logging.getLogger(__name__)

# Data Loading Functions
def get_data_loader():
    """Get the appropriate data loader function."""
    try:
        from src.core.data_loader import load_raw
        return load_raw
    except ImportError:
        try:
            from src.core.data.loader import load_programs
            return load_programs
        except ImportError:
            logger.error("No data loader available")
            return lambda: []

def get_async_data_loader():
    """Get the async data loader function."""
    try:
        from src.core.data.loader import load_university_data
        return load_university_data
    except ImportError:
        logger.error("No async data loader available")
        return lambda: []

def get_university_by_id_func():
    """Get the university by ID function."""
    try:
        from src.core.data.loader import get_university_by_id
        return get_university_by_id
    except ImportError:
        logger.error("No get_university_by_id function available")
        return lambda x: None

# Feature Engineering
def get_feature_engineering():
    """Get the feature engineering function."""
    try:
        from src.core.feature_engineering import add_derived_features
        return add_derived_features
    except ImportError:
        logger.error("No feature engineering available")
        return lambda x: x

# MCDA Functions
def get_mcda_scorer():
    """Get the MCDA scorer function."""
    try:
        from src.core.mcda.scorer import calculate_mcda_score
        return calculate_mcda_score
    except ImportError:
        try:
            from src.core.mcda import score
            return score
        except ImportError:
            logger.error("No MCDA scorer available")
            return lambda x: 0.0

def get_mcda_scorer_func():
    """Get the MCDA scorer function (alternative name)."""
    try:
        from src.core.mcda.scorer import mcda_scorer
        return mcda_scorer
    except ImportError:
        logger.error("No mcda_scorer function available")
        return lambda x: []

# ML Functions
def get_ml_recommender():
    """Get the ML recommender function."""
    try:
        from src.core.ml.model import ml_recommender
        return ml_recommender
    except ImportError:
        logger.error("No ML recommender available")
        return lambda x: []

# Recommendation Functions
def get_recommender():
    """Get the main recommender function."""
    try:
        from src.core.recommender import get_recommendations
        return get_recommendations
    except ImportError:
        try:
            from src.core.hybrid_recommender import get_recommendations
            return get_recommendations
        except ImportError:
            logger.error("No recommender available")
            return lambda x: []

# Telegram Safe Functions
def get_telegram_safe_functions():
    """Get telegram safe functions."""
    try:
        from src.bot.telegram_safe_v3 import safe_answer_callback, safe_edit_message, safe_send_message, log_telegram_errors
        return {
            'safe_answer_callback': safe_answer_callback,
            'safe_edit_message': safe_edit_message,
            'safe_send_message': safe_send_message,
            'log_telegram_errors': log_telegram_errors
        }
    except ImportError:
        try:
            from src.bot.telegram_safe import safe_answer_callback, safe_edit_message
            return {
                'safe_answer_callback': safe_answer_callback,
                'safe_edit_message': safe_edit_message,
                'safe_send_message': lambda *args, **kwargs: None,
                'log_telegram_errors': lambda func: func
            }
        except ImportError:
            logger.error("No telegram safe functions available")
            return {
                'safe_answer_callback': lambda x: None,
                'safe_edit_message': lambda *args, **kwargs: None,
                'safe_send_message': lambda *args, **kwargs: None,
                'log_telegram_errors': lambda func: func
            }

# I18n Functions
def get_i18n_functions():
    """Get i18n functions."""
    try:
        from src.bot.i18n import t, get_lang
        return {'t': t, 'get_lang': get_lang}
    except ImportError:
        logger.error("No i18n functions available")
        return {
            't': lambda key, lang: key,
            'get_lang': lambda context: 'kh'
        }

# UI Functions
def get_ui_functions():
    """Get UI functions."""
    try:
        from src.bot.ui import create_enhanced_recommendations_view
        return {'create_enhanced_recommendations_view': create_enhanced_recommendations_view}
    except ImportError:
        logger.error("No UI functions available")
        return {'create_enhanced_recommendations_view': lambda *args, **kwargs: ("Error", None)}

# Keyboard Functions
def get_keyboard_functions():
    """Get keyboard functions."""
    try:
        from src.bot.keyboards_v3 import create_assessment_keyboard, get_question_text, get_answer_text
        return {
            'create_assessment_keyboard': create_assessment_keyboard,
            'get_question_text': get_question_text,
            'get_answer_text': get_answer_text
        }
    except ImportError:
        logger.error("No keyboard functions available")
        return {
            'create_assessment_keyboard': lambda x: None,
            'get_question_text': lambda x: "Question not available",
            'get_answer_text': lambda x, y: "Answer not available"
        }

# Stats Functions
def get_bot_stats():
    """Get bot stats."""
    try:
        from src.bot.commands_v3 import bot_stats
        return bot_stats
    except ImportError:
        logger.error("No bot stats available")
        return {'total_assessments': 0}

# Validation Functions
def validate_imports():
    """Validate that all critical imports are working."""
    issues = []
    
    # Test data loader
    try:
        loader = get_data_loader()
        if callable(loader):
            logger.info("✅ Data loader available")
        else:
            issues.append("Data loader not callable")
    except Exception as e:
        issues.append(f"Data loader error: {e}")
    
    # Test telegram safe functions
    try:
        safe_funcs = get_telegram_safe_functions()
        if all(callable(func) for func in safe_funcs.values()):
            logger.info("✅ Telegram safe functions available")
        else:
            issues.append("Some telegram safe functions not callable")
    except Exception as e:
        issues.append(f"Telegram safe functions error: {e}")
    
    # Test recommender
    try:
        recommender = get_recommender()
        if callable(recommender):
            logger.info("✅ Recommender available")
        else:
            issues.append("Recommender not callable")
    except Exception as e:
        issues.append(f"Recommender error: {e}")
    
    return issues

if __name__ == "__main__":
    print("🔍 Validating EduGuideBot imports...")
    issues = validate_imports()
    if issues:
        print("❌ Import issues found:")
        for issue in issues:
            print(f"  • {issue}")
    else:
        print("✅ All imports validated successfully!")
