#!/usr/bin/env python3
"""
Test Data Validation System
Tests the enhanced data validation and loading system.
"""

import asyncio
import sys
import tempfile
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.data.loader import (
    validate_file_access, 
    validate_json_structure, 
    validate_unicode_encoding,
    load_university_data
)

def test_file_validation():
    """Test file access validation."""
    print("🧪 Testing file validation...")
    
    # Test with non-existent file
    fake_path = Path("non_existent_file.json")
    result = validate_file_access(fake_path)
    assert not result, "Should fail for non-existent file"
    print("  ✅ Non-existent file validation works")
    
    # Test with valid file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump({"test": "data"}, f)
        temp_path = Path(f.name)
    
    try:
        result = validate_file_access(temp_path)
        assert result, "Should pass for valid file"
        print("  ✅ Valid file validation works")
    finally:
        temp_path.unlink()

def test_json_validation():
    """Test JSON structure validation."""
    print("🧪 Testing JSON validation...")
    
    # Test valid structure
    valid_data = {
        "university": {
            "name_kh": "សាកលវិទ្យាល័យតេស្ត",
            "name_en": "Test University"
        },
        "majors": [
            {
                "name_kh": "វិទ្យាសាស្ត្រកុំព្យូទ័រ",
                "name_en": "Computer Science"
            }
        ]
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(valid_data, f, ensure_ascii=False)
        temp_path = Path(f.name)
    
    try:
        result = validate_json_structure(valid_data, temp_path)
        assert result, "Should pass for valid structure"
        print("  ✅ Valid JSON structure validation works")
    finally:
        temp_path.unlink()
    
    # Test invalid structure
    invalid_data = {"invalid": "structure"}
    result = validate_json_structure(invalid_data, Path("test.json"))
    assert not result, "Should fail for invalid structure"
    print("  ✅ Invalid JSON structure validation works")

def test_unicode_validation():
    """Test Unicode encoding validation."""
    print("🧪 Testing Unicode validation...")
    
    # Test with valid Unicode
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        f.write('{"test": "ភាសាខ្មែរ"}')
        temp_path = Path(f.name)
    
    try:
        result = validate_unicode_encoding(temp_path)
        assert result, "Should pass for valid Unicode"
        print("  ✅ Valid Unicode validation works")
    finally:
        temp_path.unlink()

async def test_data_loading():
    """Test actual data loading."""
    print("🧪 Testing data loading...")
    
    try:
        data = await load_university_data()
        print(f"  ✅ Loaded {len(data)} university records")
        
        if len(data) > 0:
            sample = data[0]
            required_fields = ['id', 'major_name_kh', 'university_name_kh', 'city']
            for field in required_fields:
                assert field in sample, f"Missing required field: {field}"
            print("  ✅ Data structure validation passed")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Data loading failed: {e}")
        return False

def main():
    """Run all validation tests."""
    print("🚀 EDUGUIDEBOT DATA VALIDATION TESTS")
    print("=" * 50)
    
    try:
        # Test individual validation functions
        test_file_validation()
        test_json_validation()
        test_unicode_validation()
        
        # Test actual data loading
        success = asyncio.run(test_data_loading())
        
        if success:
            print("\n✅ ALL VALIDATION TESTS PASSED!")
            print("🎯 Data validation system is working correctly.")
        else:
            print("\n❌ SOME TESTS FAILED!")
            print("🔧 Data validation system needs attention.")
            
    except Exception as e:
        print(f"\n💥 TEST SUITE CRASHED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
