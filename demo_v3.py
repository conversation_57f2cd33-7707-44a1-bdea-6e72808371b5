#!/usr/bin/env python3
"""
EduGuideBot v3 Demo Script
Demonstrates the complete bot functionality without Telegram
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def demo_assessment_flow():
    """Demo the complete assessment and recommendation flow."""
    print("🎓 EduGuideBot v3 Demo - Assessment Flow\n")
    
    # Import components
    from src.bot.keyboards_v3 import ASSESSMENT_QUESTIONS, get_question_text, get_answer_text
    from src.core.recommender import get_recommendations
    
    print("📝 Starting 16-Question Assessment...")
    print("=" * 50)
    
    # Simulate user answers
    user_answers = {}
    
    for i, question_data in enumerate(ASSESSMENT_QUESTIONS):
        question_text = question_data["question"]
        answers = question_data["answers"]
        
        print(f"\n📋 Question {i+1}/16:")
        print(f"   {question_text}")
        print("   Options:")
        for j, answer in enumerate(answers):
            print(f"   {j+1}. {answer}")
        
        # Auto-select first answer for demo
        selected_answer = 0
        user_answers[i] = {
            'answer_index': selected_answer,
            'answer_text': answers[selected_answer]
        }
        print(f"   ✅ Selected: {answers[selected_answer]}")
    
    print("\n" + "=" * 50)
    print("🎯 Generating Recommendations...")
    
    # Get recommendations
    recommendations = await get_recommendations(user_answers, top_k=5)
    
    print(f"\n🎉 Found {len(recommendations)} recommendations for you:\n")
    
    for i, rec in enumerate(recommendations, 1):
        confidence = "★★★★★" if rec['score'] > 0.8 else "★★★★☆" if rec['score'] > 0.6 else "★★★☆☆"

        print(f"{i}. {rec['major_name']}")
        print(f"   🏫 {rec['university_name']}")
        print(f"   📍 {rec['location']}")
        print(f"   💰 {rec['fees_usd']} USD/year")
        print(f"   ★ {confidence} ({rec['score']:.2f})")
        print(f"   📊 MCDA: {rec['mcda_score']:.2f} | ML: {rec['ml_score']:.2f}")
        print()
    
    return recommendations


async def demo_major_details():
    """Demo major details functionality."""
    print("\n🔍 Major Details Demo")
    print("=" * 30)
    
    from src.core.data.loader import load_university_data
    
    # Load data and show details for first major
    data = await load_university_data()
    if data:
        major = data[0]
        print(f"📚 Major: {major.get('major_name_kh', major.get('major_name_en', 'N/A'))}")
        print(f"🏫 University: {major.get('university_name_kh', major.get('university_name_en', 'N/A'))}")
        print(f"📍 Location: {major.get('city', 'N/A')}")
        print(f"💰 Fees: {major.get('tuition_fees_usd', 'N/A')} USD")
        print(f"⏱️ Duration: {major.get('duration_years', 'N/A')} years")
        print(f"📊 Employment Rate: {major.get('employment_rate', 'N/A')}%")


async def demo_ux_simulation():
    """Demo UX simulation."""
    print("\n🎮 UX Simulation Demo")
    print("=" * 25)
    
    from tools.ux_simulator_v3 import run_ux_simulation
    
    print("Running automated UX tests...")
    results = await run_ux_simulation()
    
    print(f"✅ Tests Passed: {results['passed']}")
    print(f"❌ Tests Failed: {results['failed']}")
    print(f"⏱️ Runtime: {results['runtime']:.2f} seconds")
    
    if results['suggestions']:
        print("\n💡 Suggestions:")
        for suggestion in results['suggestions']:
            print(f"   • {suggestion}")


async def demo_bot_commands():
    """Demo bot command functionality."""
    print("\n🤖 Bot Commands Demo")
    print("=" * 22)
    
    from src.bot.commands_v3 import bot_stats
    from unittest.mock import AsyncMock
    
    # Mock update and context
    update = AsyncMock()
    update.message.reply_text = AsyncMock()
    context = AsyncMock()
    context.user_data = {}
    
    # Demo start command
    from src.bot.commands_v3 import start_command
    print("📱 /start command:")
    await start_command(update, context)
    print("   ✅ Language selection shown")
    
    # Demo status command
    from src.bot.commands_v3 import ai_status_command
    print("\n📊 /status command:")
    await ai_status_command(update, context)
    print("   ✅ Bot status displayed")
    
    print(f"\n📈 Current Stats:")
    print(f"   • Active Sessions: {bot_stats['active_sessions']}")
    print(f"   • Total Assessments: {bot_stats['total_assessments']}")
    print(f"   • Error Count: {bot_stats['error_count']}")


async def main():
    """Run complete demo."""
    print("🚀 EduGuideBot v3 Complete Demo")
    print("=" * 40)
    print("This demo shows all bot functionality without Telegram")
    print()
    
    try:
        # Demo 1: Assessment Flow
        recommendations = await demo_assessment_flow()
        
        # Demo 2: Major Details
        await demo_major_details()
        
        # Demo 3: UX Simulation
        await demo_ux_simulation()
        
        # Demo 4: Bot Commands
        await demo_bot_commands()
        
        print("\n" + "=" * 40)
        print("🎉 Demo Complete!")
        print("\n📋 Summary:")
        print(f"   • Assessment: 16 questions answered")
        print(f"   • Recommendations: {len(recommendations)} generated")
        print(f"   • All systems: ✅ Working")
        print("\n🚀 Ready to run with real Telegram bot!")
        print("   Set BOT_TOKEN and run: python main.py")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
